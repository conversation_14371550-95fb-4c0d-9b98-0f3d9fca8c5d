'use client'
import { useEffect, useState } from 'react'
import { DepartmentFormSkeleton } from '../../../components/skeletons'
import Link from 'next/link'
import { debounce, isEmpty, trim } from 'lodash'
import { useLazyQuery, useMutation } from '@apollo/client'
import { CreateDepartment, UpdateDepartment } from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { ReadOneDepartment } from '@/app/lib/graphQL/query'
import DepartmentDropdown from './dropdown'
import CrewMultiSelectDropdown from '../crew/multiselect-dropdown/multiselect-dropdown'

import { isAdmin, preventCrewAccess } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button, H1 } from '@/components/ui'
import { ArrowLeft } from 'lucide-react'

const DepartmentForm = ({ departmentId = 0 }: { departmentId?: number }) => {
    const [isUserAdmin, setIsUserAdmin] = useState<any>(-1)
    const router = useRouter()
    const [department, setDepartment] = useState({} as any)
    const [formErrors, setFormErrors] = useState({} as any)
    const [isParent, setIsParent] = useState(false)
    const [departmentMemberIDs, setDepartmentMemberIDs] = useState([] as any)
    const handleInputOnChange = debounce((event: any) => {
        const { name, value } = event.target
        setDepartment({ ...department, [name]: trim(value) })
    }, 600)
    const [createDepartment, { loading: loadingCreateDepartment }] =
        useMutation(CreateDepartment, {
            onCompleted: (data: any) => {
                router.push(`/department/info?id=${data.createDepartment.id}`)
            },
            onError: (error: any) => {
                console.error('createDepartment', error)
            },
        })
    const [updateDepartment, { loading: loadingUpdateDepartment }] =
        useMutation(UpdateDepartment, {
            onCompleted: (data: any) => {
                router.push(`/department/info?id=${data.updateDepartment.id}`)
            },
            onError: (error: any) => {
                console.error('updateDepartment', error)
            },
        })
    useEffect(() => {
        setIsUserAdmin(isAdmin())
    }, [])
    const handleSave = async () => {
        setFormErrors({})
        // validate title
        if (isEmpty(trim(department.title))) {
            setFormErrors({
                ...formErrors,
                title: 'Department name is required',
            })
            return
        }
        const input = {
            id: departmentId,
            title: department.title,
            parentID: department.parentID,
            seaLogsMembers: departmentMemberIDs.join(','),
        }
        if (departmentId === 0) {
            await createDepartment({
                variables: {
                    input: input,
                },
            })
        } else {
            await updateDepartment({
                variables: {
                    input: input,
                },
            })
        }
    }
    const [readOneDepartment, { loading: loadingReadOneDepartment }] =
        useLazyQuery(ReadOneDepartment, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readOneDepartment
                setDepartment(data)
                setIsParent(data.parentID === '0')
                const deptMemberIDs = data.seaLogsMembers.nodes.map(
                    (m: any) => m.id,
                )
                setDepartmentMemberIDs(deptMemberIDs)
            },
            onError: (error: any) => {
                console.error('readOneDepartment', error)
            },
        })
    const loadDepartment = async () => {
        await readOneDepartment({
            variables: {
                id: departmentId,
            },
        })
    }
    const handleOnChangeParent = (item: any) => {
        setDepartment({ ...department, parentID: item.value })
    }
    const handleOnChangeMember = (item: any) => {
        setDepartmentMemberIDs(item.map((i: any) => i.value))
    }
    useEffect(() => {
        if (departmentId > 0) {
            loadDepartment()
        }
    }, [departmentId])

    useEffect(() => {
        preventCrewAccess()
    }, [])
    return (
        <div className="w-full p-0">
            {isUserAdmin === false ? (
                <Loading errorMessage="Oops You do not have the permission to view this section." />
            ) : (
                <>
                    <div className="flex justify-between pb-4 pt-3">
                        <H1>
                            {departmentId === 0 ? 'New' : 'Edit'} Department
                        </H1>
                    </div>
                    {!department && departmentId > 0 ? (
                        <DepartmentFormSkeleton />
                    ) : (
                        <div className="px-0 md:px-4 pt-4 border-t  ">
                            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                                <div className="my-4 ">
                                    Department Details
                                    <p className=" mt-4 max-w-[25rem] leading-loose">
                                        Lorem ipsum dolor sit amet consectetur
                                        adipisicing elit. Facilis possimus harum
                                        eaque itaque est id reprehenderit
                                        excepturi eius temporibus, illo officia
                                        amet nobis sapiente dolorem ipsa earum
                                        adipisci recusandae cumque.
                                    </p>
                                </div>
                                <div className="col-span-2">
                                    <div className="flex flex-col w-full gap-4">
                                        <div className="w-full">
                                            <div className="w-full my-4 flex flex-col">
                                                <Label className="mb-1 ">
                                                    Name
                                                </Label>
                                                <Input
                                                    name="title"
                                                    type="text"
                                                    defaultValue={
                                                        department?.title
                                                    }
                                                    required
                                                    onChange={
                                                        handleInputOnChange
                                                    }
                                                />
                                                {formErrors?.title && (
                                                    <small className="text-red-500">
                                                        {formErrors.title}
                                                    </small>
                                                )}
                                            </div>
                                        </div>
                                        {!isParent && (
                                            <div className="w-full">
                                                <div className="w-full my-4 flex flex-col">
                                                    <Label className="mb-1 ">
                                                        Head Department
                                                    </Label>
                                                    <DepartmentDropdown
                                                        excludeId={departmentId}
                                                        value={
                                                            department.parentID
                                                        }
                                                        onChange={
                                                            handleOnChangeParent
                                                        }
                                                    />
                                                </div>
                                            </div>
                                        )}
                                        {department.children?.nodes.length >
                                            0 && (
                                            <div className="px-0 md:px-4 pt-4 border-t  ">
                                                <div className="grid grid-cols-3 gap-6 py-4 px-4">
                                                    <div>Sub Departments</div>
                                                    <div className="col-span-2">
                                                        {department.children.nodes.map(
                                                            (child: any) => (
                                                                <div
                                                                    key={
                                                                        child.id
                                                                    }
                                                                    className="mb-2">
                                                                    <Link
                                                                        href={`/department/info?id=${child.id}`}
                                                                        className="flex items-center">
                                                                        <div className=" text-medium ">
                                                                            {
                                                                                child.title
                                                                            }
                                                                        </div>
                                                                    </Link>
                                                                </div>
                                                            ),
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                        <div className="px-0 md:px-4 pt-4 border-t  ">
                                            <div className="grid grid-cols-3 gap-6 py-4 px-4">
                                                <div>Members</div>
                                                <div className="col-span-2">
                                                    <CrewMultiSelectDropdown
                                                        value={
                                                            departmentMemberIDs
                                                        }
                                                        onChange={
                                                            handleOnChangeMember
                                                        }
                                                        filterByAdmin={true}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr className="mb-4" />
                            <div className="flex justify-end px-4 pb-4 pt-4">
                                <Link
                                    href={
                                        departmentId == 0
                                            ? '/department'
                                            : `/department/info?id=${departmentId}`
                                    }
                                    className={`inline-flex justify-center items-center`}>
                                    <Button iconLeft={ArrowLeft}>Cancel</Button>
                                </Link>

                                <Button
                                    onClick={handleSave}
                                    disabled={
                                        loadingCreateDepartment ||
                                        loadingUpdateDepartment ||
                                        loadingReadOneDepartment
                                    }>
                                    {departmentId === 0
                                        ? 'Create Department'
                                        : 'Update Department'}
                                </Button>
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    )
}

export default DepartmentForm
