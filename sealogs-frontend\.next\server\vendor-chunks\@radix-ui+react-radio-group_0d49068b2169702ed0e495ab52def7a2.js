"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-radio-group_0d49068b2169702ed0e495ab52def7a2";
exports.ids = ["vendor-chunks/@radix-ui+react-radio-group_0d49068b2169702ed0e495ab52def7a2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_0d49068b2169702ed0e495ab52def7a2/node_modules/@radix-ui/react-radio-group/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-radio-group_0d49068b2169702ed0e495ab52def7a2/node_modules/@radix-ui/react-radio-group/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioGroupIndicator: () => (/* binding */ RadioGroupIndicator),\n/* harmony export */   RadioGroupItem: () => (/* binding */ RadioGroupItem),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   createRadioGroupScope: () => (/* binding */ createRadioGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_4643b1fdd6fb66fffe6bde151b53d8db/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_c1d094e87a5d15151b02b9089f171906/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_048355405bf23f6949c78f7e03b8eace/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_66602f98bf01f81acbd444ba3bbb16e0/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_040c0a1815831db229bf023295faa27f/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_2c2cbd66f7e179ac919545cdfd463fdc/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._b40803a16ab2a37c6154b9eb783b5bdd/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_b4f510d11ec803491b821601b4df5874/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._1353232229eeebe44bc4a117f3d5d6a5/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Indicator,Item,RadioGroup,RadioGroupIndicator,RadioGroupItem,Root,createRadioGroupScope auto */ // packages/react/radio-group/src/radio-group.tsx\n\n\n\n\n\n\n\n\n\n// packages/react/radio-group/src/radio.tsx\n\n\n\n\n\n\n\n\n\nvar RADIO_NAME = \"Radio\";\nvar [createRadioContext, createRadioScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_NAME);\nvar [RadioProvider, useRadioContext] = createRadioContext(RADIO_NAME);\nvar Radio = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, name, checked = false, required, disabled, value = \"on\", onCheck, form, ...radioProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(RadioProvider, {\n        scope: __scopeRadio,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.button, {\n                type: \"button\",\n                role: \"radio\",\n                \"aria-checked\": checked,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...radioProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onClick, (event)=>{\n                    if (!checked) onCheck?.();\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nRadio.displayName = RADIO_NAME;\nvar INDICATOR_NAME = \"RadioIndicator\";\nvar RadioIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || context.checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    });\n});\nRadioIndicator.displayName = INDICATOR_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            setChecked.call(input, checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"radio\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\n// packages/react/radio-group/src/radio-group.tsx\n\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar RADIO_GROUP_NAME = \"RadioGroup\";\nvar [createRadioGroupContext, createRadioGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(RADIO_GROUP_NAME, [\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope,\n    createRadioScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.createRovingFocusGroupScope)();\nvar useRadioScope = createRadioScope();\nvar [RadioGroupProvider, useRadioGroupContext] = createRadioGroupContext(RADIO_GROUP_NAME);\nvar RadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, name, defaultValue, value: valueProp, required = false, disabled = false, orientation, dir, loop = true, onValueChange, ...groupProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_10__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_11__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: __scopeRadioGroup,\n        name,\n        required,\n        disabled,\n        value,\n        onValueChange: setValue,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Root, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            orientation,\n            dir: direction,\n            loop,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                role: \"radiogroup\",\n                \"aria-required\": required,\n                \"aria-orientation\": orientation,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                dir: direction,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRadioGroup.displayName = RADIO_GROUP_NAME;\nvar ITEM_NAME = \"RadioGroupItem\";\nvar RadioGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (ARROW_KEYS.includes(event.key)) {\n                isArrowKeyPressedRef.current = true;\n            }\n        };\n        const handleKeyUp = ()=>isArrowKeyPressedRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown);\n        document.addEventListener(\"keyup\", handleKeyUp);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n            document.removeEventListener(\"keyup\", handleKeyUp);\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_9__.Item, {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !isDisabled,\n        active: checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Radio, {\n            disabled: isDisabled,\n            required: context.required,\n            checked,\n            ...radioScope,\n            ...itemProps,\n            name: context.name,\n            ref: composedRefs,\n            onCheck: ()=>context.onValueChange(itemProps.value),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)((event)=>{\n                if (event.key === \"Enter\") event.preventDefault();\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(itemProps.onFocus, ()=>{\n                if (isArrowKeyPressedRef.current) ref.current?.click();\n            })\n        })\n    });\n});\nRadioGroupItem.displayName = ITEM_NAME;\nvar INDICATOR_NAME2 = \"RadioGroupIndicator\";\nvar RadioGroupIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioIndicator, {\n        ...radioScope,\n        ...indicatorProps,\n        ref: forwardedRef\n    });\n});\nRadioGroupIndicator.displayName = INDICATOR_NAME2;\nvar Root2 = RadioGroup;\nvar Item2 = RadioGroupItem;\nvar Indicator = RadioGroupIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-radio-group_0d49068b2169702ed0e495ab52def7a2/node_modules/@radix-ui/react-radio-group/dist/index.mjs\n");

/***/ })

};
;