"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dropdown-me_fb9f8f03a80e7aaab5b8db38131e1c36";
exports.ids = ["vendor-chunks/@radix-ui+react-dropdown-me_fb9f8f03a80e7aaab5b8db38131e1c36"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_fb9f8f03a80e7aaab5b8db38131e1c36/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dropdown-me_fb9f8f03a80e7aaab5b8db38131e1c36/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuArrow: () => (/* binding */ DropdownMenuArrow),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuItemIndicator: () => (/* binding */ DropdownMenuItemIndicator),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger),\n/* harmony export */   Group: () => (/* binding */ Group2),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator2),\n/* harmony export */   Label: () => (/* binding */ Label2),\n/* harmony export */   Portal: () => (/* binding */ Portal2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup2),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem2),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Separator: () => (/* binding */ Separator2),\n/* harmony export */   Sub: () => (/* binding */ Sub2),\n/* harmony export */   SubContent: () => (/* binding */ SubContent2),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createDropdownMenuScope: () => (/* binding */ createDropdownMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_df97a09d62161aad15439954749cb418/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-menu */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.6__79cf286ddb0d2531a67d026825b4a4f2/node_modules/@radix-ui/react-menu/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,CheckboxItem,Content,DropdownMenu,DropdownMenuArrow,DropdownMenuCheckboxItem,DropdownMenuContent,DropdownMenuGroup,DropdownMenuItem,DropdownMenuItemIndicator,DropdownMenuLabel,DropdownMenuPortal,DropdownMenuRadioGroup,DropdownMenuRadioItem,DropdownMenuSeparator,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuTrigger,Group,Item,ItemIndicator,Label,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,Trigger,createDropdownMenuScope auto */ // packages/react/dropdown-menu/src/dropdown-menu.tsx\n\n\n\n\n\n\n\n\n\n\nvar DROPDOWN_MENU_NAME = \"DropdownMenu\";\nvar [createDropdownMenuContext, createDropdownMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DROPDOWN_MENU_NAME, [\n    _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope\n]);\nvar useMenuScope = (0,_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope)();\nvar [DropdownMenuProvider, useDropdownMenuContext] = createDropdownMenuContext(DROPDOWN_MENU_NAME);\nvar DropdownMenu = (props)=>{\n    const { __scopeDropdownMenu, children, dir, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DropdownMenuProvider, {\n        scope: __scopeDropdownMenu,\n        triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        triggerRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {\n            ...menuScope,\n            open,\n            onOpenChange: setOpen,\n            dir,\n            modal,\n            children\n        })\n    });\n};\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\nvar TRIGGER_NAME = \"DropdownMenuTrigger\";\nvar DropdownMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...menuScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n            type: \"button\",\n            id: context.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": context.open ? context.contentId : void 0,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            ...triggerProps,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.composeRefs)(forwardedRef, context.triggerRef),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                    context.onOpenToggle();\n                    if (!context.open) event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (disabled) return;\n                if ([\n                    \"Enter\",\n                    \" \"\n                ].includes(event.key)) context.onOpenToggle();\n                if (event.key === \"ArrowDown\") context.onOpenChange(true);\n                if ([\n                    \"Enter\",\n                    \" \",\n                    \"ArrowDown\"\n                ].includes(event.key)) event.preventDefault();\n            })\n        })\n    });\n});\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DropdownMenuPortal\";\nvar DropdownMenuPortal = (props)=>{\n    const { __scopeDropdownMenu, ...portalProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        ...menuScope,\n        ...portalProps\n    });\n};\nDropdownMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"DropdownMenuContent\";\nvar DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        id: context.contentId,\n        \"aria-labelledby\": context.triggerId,\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            hasInteractedOutsideRef.current = false;\n            event.preventDefault();\n        }),\n        onInteractOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onInteractOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        }),\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"DropdownMenuGroup\";\nvar DropdownMenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        ...menuScope,\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"DropdownMenuLabel\";\nvar DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ...menuScope,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"DropdownMenuItem\";\nvar DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ...menuScope,\n        ...itemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"DropdownMenuCheckboxItem\";\nvar DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ...menuScope,\n        ...checkboxItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"DropdownMenuRadioGroup\";\nvar DropdownMenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n        ...menuScope,\n        ...radioGroupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"DropdownMenuRadioItem\";\nvar DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ...menuScope,\n        ...radioItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"DropdownMenuItemIndicator\";\nvar DropdownMenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n        ...menuScope,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"DropdownMenuSeparator\";\nvar DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ...menuScope,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"DropdownMenuArrow\";\nvar DropdownMenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...menuScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuArrow.displayName = ARROW_NAME;\nvar DropdownMenuSub = (props)=>{\n    const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {\n        ...menuScope,\n        open,\n        onOpenChange: setOpen,\n        children\n    });\n};\nvar SUB_TRIGGER_NAME = \"DropdownMenuSubTrigger\";\nvar DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ...menuScope,\n        ...subTriggerProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"DropdownMenuSubContent\";\nvar DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ...menuScope,\n        ...subContentProps,\n        ref: forwardedRef,\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\nvar Root2 = DropdownMenu;\nvar Trigger = DropdownMenuTrigger;\nvar Portal2 = DropdownMenuPortal;\nvar Content2 = DropdownMenuContent;\nvar Group2 = DropdownMenuGroup;\nvar Label2 = DropdownMenuLabel;\nvar Item2 = DropdownMenuItem;\nvar CheckboxItem2 = DropdownMenuCheckboxItem;\nvar RadioGroup2 = DropdownMenuRadioGroup;\nvar RadioItem2 = DropdownMenuRadioItem;\nvar ItemIndicator2 = DropdownMenuItemIndicator;\nvar Separator2 = DropdownMenuSeparator;\nvar Arrow2 = DropdownMenuArrow;\nvar Sub2 = DropdownMenuSub;\nvar SubTrigger2 = DropdownMenuSubTrigger;\nvar SubContent2 = DropdownMenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_fb9f8f03a80e7aaab5b8db38131e1c36/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\n");

/***/ })

};
;