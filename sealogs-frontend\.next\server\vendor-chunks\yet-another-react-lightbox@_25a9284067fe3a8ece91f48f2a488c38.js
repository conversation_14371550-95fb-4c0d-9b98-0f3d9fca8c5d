"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38";
exports.ids = ["vendor-chunks/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0bccefafe1ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3hAXzI1YTkyODQwNjdmZTNhOGVjZTkxZjQ4ZjJhNDg4YzM4L25vZGVfbW9kdWxlcy95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveC9kaXN0L3BsdWdpbnMvY2FwdGlvbnMvY2FwdGlvbnMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveEBfMjVhOTI4NDA2N2ZlM2E4ZWNlOTFmNDhmMmE0ODhjMzgvbm9kZV9tb2R1bGVzL3lldC1hbm90aGVyLXJlYWN0LWxpZ2h0Ym94L2Rpc3QvcGx1Z2lucy9jYXB0aW9ucy9jYXB0aW9ucy5jc3M/MzYzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBiY2NlZmFmZTFjZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/captions.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/styles.css":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/styles.css ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"075a87508db5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3hAXzI1YTkyODQwNjdmZTNhOGVjZTkxZjQ4ZjJhNDg4YzM4L25vZGVfbW9kdWxlcy95ZXQtYW5vdGhlci1yZWFjdC1saWdodGJveC9kaXN0L3N0eWxlcy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3lldC1hbm90aGVyLXJlYWN0LWxpZ2h0Ym94QF8yNWE5Mjg0MDY3ZmUzYThlY2U5MWY0OGYyYTQ4OGMzOC9ub2RlX21vZHVsZXMveWV0LWFub3RoZXItcmVhY3QtbGlnaHRib3gvZGlzdC9zdHlsZXMuY3NzP2JhNTIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNzVhODc1MDhkYjVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/styles.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   Carousel: () => (/* binding */ Carousel),\n/* harmony export */   CarouselModule: () => (/* binding */ CarouselModule),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   ControllerContext: () => (/* binding */ ControllerContext),\n/* harmony export */   ControllerModule: () => (/* binding */ ControllerModule),\n/* harmony export */   DocumentContext: () => (/* binding */ DocumentContext),\n/* harmony export */   DocumentContextProvider: () => (/* binding */ DocumentContextProvider),\n/* harmony export */   ELEMENT_BUTTON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL),\n/* harmony export */   ErrorIcon: () => (/* binding */ ErrorIcon),\n/* harmony export */   EventsContext: () => (/* binding */ EventsContext),\n/* harmony export */   EventsProvider: () => (/* binding */ EventsProvider),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER),\n/* harmony export */   IconButton: () => (/* binding */ IconButton),\n/* harmony export */   ImageSlide: () => (/* binding */ ImageSlide),\n/* harmony export */   Lightbox: () => (/* binding */ Lightbox),\n/* harmony export */   LightboxDefaultProps: () => (/* binding */ LightboxDefaultProps),\n/* harmony export */   LightboxDispatchContext: () => (/* binding */ LightboxDispatchContext),\n/* harmony export */   LightboxPropsContext: () => (/* binding */ LightboxPropsContext),\n/* harmony export */   LightboxPropsProvider: () => (/* binding */ LightboxPropsProvider),\n/* harmony export */   LightboxRoot: () => (/* binding */ LightboxRoot),\n/* harmony export */   LightboxStateContext: () => (/* binding */ LightboxStateContext),\n/* harmony export */   LightboxStateProvider: () => (/* binding */ LightboxStateProvider),\n/* harmony export */   LoadingIcon: () => (/* binding */ LoadingIcon),\n/* harmony export */   MODULE_CAROUSEL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR),\n/* harmony export */   Navigation: () => (/* binding */ Navigation),\n/* harmony export */   NavigationButton: () => (/* binding */ NavigationButton),\n/* harmony export */   NavigationModule: () => (/* binding */ NavigationModule),\n/* harmony export */   NextIcon: () => (/* binding */ NextIcon),\n/* harmony export */   NoScroll: () => (/* binding */ NoScroll),\n/* harmony export */   NoScrollModule: () => (/* binding */ NoScrollModule),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   PortalModule: () => (/* binding */ PortalModule),\n/* harmony export */   PreviousIcon: () => (/* binding */ PreviousIcon),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RootModule: () => (/* binding */ RootModule),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLAYING),\n/* harmony export */   SwipeState: () => (/* binding */ SwipeState),\n/* harmony export */   TimeoutsContext: () => (/* binding */ TimeoutsContext),\n/* harmony export */   TimeoutsProvider: () => (/* binding */ TimeoutsProvider),\n/* harmony export */   Toolbar: () => (/* binding */ Toolbar),\n/* harmony export */   ToolbarModule: () => (/* binding */ ToolbarModule),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus),\n/* harmony export */   addToolbarButton: () => (/* binding */ addToolbarButton),\n/* harmony export */   calculatePreload: () => (/* binding */ calculatePreload),\n/* harmony export */   cleanup: () => (/* binding */ cleanup),\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   composePrefix: () => (/* binding */ composePrefix),\n/* harmony export */   computeSlideRect: () => (/* binding */ computeSlideRect),\n/* harmony export */   createIcon: () => (/* binding */ createIcon),\n/* harmony export */   createIconDisabled: () => (/* binding */ createIconDisabled),\n/* harmony export */   createModule: () => (/* binding */ createModule),\n/* harmony export */   createNode: () => (/* binding */ createNode),\n/* harmony export */   cssClass: () => (/* binding */ cssClass),\n/* harmony export */   cssVar: () => (/* binding */ cssVar),\n/* harmony export */   \"default\": () => (/* binding */ Lightbox),\n/* harmony export */   devicePixelRatio: () => (/* binding */ devicePixelRatio),\n/* harmony export */   getSlide: () => (/* binding */ getSlide),\n/* harmony export */   getSlideIfPresent: () => (/* binding */ getSlideIfPresent),\n/* harmony export */   getSlideIndex: () => (/* binding */ getSlideIndex),\n/* harmony export */   getSlideKey: () => (/* binding */ getSlideKey),\n/* harmony export */   hasSlides: () => (/* binding */ hasSlides),\n/* harmony export */   hasWindow: () => (/* binding */ hasWindow),\n/* harmony export */   isImageFitCover: () => (/* binding */ isImageFitCover),\n/* harmony export */   isImageSlide: () => (/* binding */ isImageSlide),\n/* harmony export */   label: () => (/* binding */ label),\n/* harmony export */   makeComposePrefix: () => (/* binding */ makeComposePrefix),\n/* harmony export */   makeInertWhen: () => (/* binding */ makeInertWhen),\n/* harmony export */   makeUseContext: () => (/* binding */ makeUseContext),\n/* harmony export */   parseInt: () => (/* binding */ parseInt),\n/* harmony export */   parseLengthPercentage: () => (/* binding */ parseLengthPercentage),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   setRef: () => (/* binding */ setRef),\n/* harmony export */   stopNavigationEventsPropagation: () => (/* binding */ stopNavigationEventsPropagation),\n/* harmony export */   useAnimation: () => (/* binding */ useAnimation),\n/* harmony export */   useContainerRect: () => (/* binding */ useContainerRect),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useDelay: () => (/* binding */ useDelay),\n/* harmony export */   useDocumentContext: () => (/* binding */ useDocumentContext),\n/* harmony export */   useEventCallback: () => (/* binding */ useEventCallback),\n/* harmony export */   useEvents: () => (/* binding */ useEvents),\n/* harmony export */   useForkRef: () => (/* binding */ useForkRef),\n/* harmony export */   useKeyboardNavigation: () => (/* binding */ useKeyboardNavigation),\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect),\n/* harmony export */   useLightboxDispatch: () => (/* binding */ useLightboxDispatch),\n/* harmony export */   useLightboxProps: () => (/* binding */ useLightboxProps),\n/* harmony export */   useLightboxState: () => (/* binding */ useLightboxState),\n/* harmony export */   useLoseFocus: () => (/* binding */ useLoseFocus),\n/* harmony export */   useMotionPreference: () => (/* binding */ useMotionPreference),\n/* harmony export */   useNavigationState: () => (/* binding */ useNavigationState),\n/* harmony export */   usePointerEvents: () => (/* binding */ usePointerEvents),\n/* harmony export */   usePointerSwipe: () => (/* binding */ usePointerSwipe),\n/* harmony export */   usePreventWheelDefaults: () => (/* binding */ usePreventWheelDefaults),\n/* harmony export */   useRTL: () => (/* binding */ useRTL),\n/* harmony export */   useSensors: () => (/* binding */ useSensors),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useTimeouts: () => (/* binding */ useTimeouts),\n/* harmony export */   useWheelSwipe: () => (/* binding */ useWheelSwipe),\n/* harmony export */   withPlugins: () => (/* binding */ withPlugins)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ ACTIVE_SLIDE_COMPLETE,ACTIVE_SLIDE_ERROR,ACTIVE_SLIDE_LOADING,ACTIVE_SLIDE_PLAYING,CLASS_FULLSIZE,CLASS_SLIDE_WRAPPER_INTERACTIVE,PLUGIN_CAPTIONS,PLUGIN_COUNTER,PLUGIN_DOWNLOAD,PLUGIN_FULLSCREEN,PLUGIN_INLINE,PLUGIN_SHARE,PLUGIN_SLIDESHOW,PLUGIN_THUMBNAILS,PLUGIN_ZOOM,SLIDE_STATUS_PLAYING,ACTION_CLOSE,ACTION_NEXT,ACTION_PREV,ACTION_SWIPE,CLASS_FLEX_CENTER,CLASS_NO_SCROLL,CLASS_NO_SCROLL_PADDING,CLASS_SLIDE_WRAPPER,Carousel,CarouselModule,CloseIcon,Controller,ControllerContext,ControllerModule,DocumentContext,DocumentContextProvider,ELEMENT_BUTTON,ELEMENT_ICON,EVENT_ON_KEY_DOWN,EVENT_ON_KEY_UP,EVENT_ON_POINTER_CANCEL,EVENT_ON_POINTER_DOWN,EVENT_ON_POINTER_LEAVE,EVENT_ON_POINTER_MOVE,EVENT_ON_POINTER_UP,EVENT_ON_WHEEL,ErrorIcon,EventsContext,EventsProvider,IMAGE_FIT_CONTAIN,IMAGE_FIT_COVER,IconButton,ImageSlide,Lightbox,LightboxDefaultProps,LightboxDispatchContext,LightboxPropsContext,LightboxPropsProvider,LightboxRoot,LightboxStateContext,LightboxStateProvider,LoadingIcon,MODULE_CAROUSEL,MODULE_CONTROLLER,MODULE_NAVIGATION,MODULE_NO_SCROLL,MODULE_PORTAL,MODULE_ROOT,MODULE_TOOLBAR,Navigation,NavigationButton,NavigationModule,NextIcon,NoScroll,NoScrollModule,Portal,PortalModule,PreviousIcon,Root,RootModule,SLIDE_STATUS_COMPLETE,SLIDE_STATUS_ERROR,SLIDE_STATUS_LOADING,SLIDE_STATUS_PLACEHOLDER,SwipeState,TimeoutsContext,TimeoutsProvider,Toolbar,ToolbarModule,UNKNOWN_ACTION_TYPE,VK_ARROW_LEFT,VK_ARROW_RIGHT,VK_ESCAPE,activeSlideStatus,addToolbarButton,calculatePreload,cleanup,clsx,composePrefix,computeSlideRect,createIcon,createIconDisabled,createModule,createNode,cssClass,cssVar,default,devicePixelRatio,getSlide,getSlideIfPresent,getSlideIndex,getSlideKey,hasSlides,hasWindow,isImageFitCover,isImageSlide,label,makeComposePrefix,makeInertWhen,makeUseContext,parseInt,parseLengthPercentage,round,setRef,stopNavigationEventsPropagation,useAnimation,useContainerRect,useController,useDelay,useDocumentContext,useEventCallback,useEvents,useForkRef,useKeyboardNavigation,useLayoutEffect,useLightboxDispatch,useLightboxProps,useLightboxState,useLoseFocus,useMotionPreference,useNavigationState,usePointerEvents,usePointerSwipe,usePreventWheelDefaults,useRTL,useSensors,useThrottle,useTimeouts,useWheelSwipe,withPlugins auto */ \n\n\n\nconst cssPrefix$3 = \"yarl__\";\nfunction clsx(...classes) {\n    return [\n        ...classes\n    ].filter(Boolean).join(\" \");\n}\nfunction cssClass(name) {\n    return `${cssPrefix$3}${name}`;\n}\nfunction cssVar(name) {\n    return `--${cssPrefix$3}${name}`;\n}\nfunction composePrefix(base, prefix) {\n    return `${base}${prefix ? `_${prefix}` : \"\"}`;\n}\nfunction makeComposePrefix(base) {\n    return (prefix)=>composePrefix(base, prefix);\n}\nfunction label(labels, defaultLabel) {\n    var _a;\n    return (_a = labels === null || labels === void 0 ? void 0 : labels[defaultLabel]) !== null && _a !== void 0 ? _a : defaultLabel;\n}\nfunction cleanup(...cleaners) {\n    return ()=>{\n        cleaners.forEach((cleaner)=>{\n            cleaner();\n        });\n    };\n}\nfunction makeUseContext(name, contextName, context) {\n    return ()=>{\n        const ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n        if (!ctx) {\n            throw new Error(`${name} must be used within a ${contextName}.Provider`);\n        }\n        return ctx;\n    };\n}\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction round(value, decimals = 0) {\n    const factor = 10 ** decimals;\n    return Math.round((value + Number.EPSILON) * factor) / factor;\n}\nfunction isImageSlide(slide) {\n    return slide.type === undefined || slide.type === \"image\";\n}\nfunction isImageFitCover(image, imageFit) {\n    return image.imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER || image.imageFit !== _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN && imageFit === _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_COVER;\n}\nfunction parseInt(value) {\n    return typeof value === \"string\" ? Number.parseInt(value, 10) : value;\n}\nfunction parseLengthPercentage(input) {\n    if (typeof input === \"number\") {\n        return {\n            pixel: input\n        };\n    }\n    if (typeof input === \"string\") {\n        const value = parseInt(input);\n        return input.endsWith(\"%\") ? {\n            percent: value\n        } : {\n            pixel: value\n        };\n    }\n    return {\n        pixel: 0\n    };\n}\nfunction computeSlideRect(containerRect, padding) {\n    const paddingValue = parseLengthPercentage(padding);\n    const paddingPixels = paddingValue.percent !== undefined ? containerRect.width / 100 * paddingValue.percent : paddingValue.pixel;\n    return {\n        width: Math.max(containerRect.width - 2 * paddingPixels, 0),\n        height: Math.max(containerRect.height - 2 * paddingPixels, 0)\n    };\n}\nfunction devicePixelRatio() {\n    return (hasWindow() ? window === null || window === void 0 ? void 0 : window.devicePixelRatio : undefined) || 1;\n}\nfunction getSlideIndex(index, slidesCount) {\n    return slidesCount > 0 ? (index % slidesCount + slidesCount) % slidesCount : 0;\n}\nfunction hasSlides(slides) {\n    return slides.length > 0;\n}\nfunction getSlide(slides, index) {\n    return slides[getSlideIndex(index, slides.length)];\n}\nfunction getSlideIfPresent(slides, index) {\n    return hasSlides(slides) ? getSlide(slides, index) : undefined;\n}\nfunction getSlideKey(slide) {\n    return isImageSlide(slide) ? slide.src : undefined;\n}\nfunction addToolbarButton(toolbar, key, button) {\n    if (!button) return toolbar;\n    const { buttons, ...restToolbar } = toolbar;\n    const index = buttons.findIndex((item)=>item === key);\n    const buttonWithKey = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(button) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(button, {\n        key\n    }, null) : button;\n    if (index >= 0) {\n        const result = [\n            ...buttons\n        ];\n        result.splice(index, 1, buttonWithKey);\n        return {\n            buttons: result,\n            ...restToolbar\n        };\n    }\n    return {\n        buttons: [\n            buttonWithKey,\n            ...buttons\n        ],\n        ...restToolbar\n    };\n}\nfunction stopNavigationEventsPropagation() {\n    const stopPropagation = (event)=>{\n        event.stopPropagation();\n    };\n    return {\n        onPointerDown: stopPropagation,\n        onKeyDown: stopPropagation,\n        onWheel: stopPropagation\n    };\n}\nfunction calculatePreload(carousel, slides, minimum = 0) {\n    return Math.min(carousel.preload, Math.max(carousel.finite ? slides.length - 1 : Math.floor(slides.length / 2), minimum));\n}\nconst isReact19 = Number(react__WEBPACK_IMPORTED_MODULE_0__.version.split(\".\")[0]) >= 19;\nfunction makeInertWhen(condition) {\n    const legacyValue = condition ? \"\" : undefined;\n    return {\n        inert: isReact19 ? condition : legacyValue\n    };\n}\nconst LightboxDefaultProps = {\n    open: false,\n    close: ()=>{},\n    index: 0,\n    slides: [],\n    render: {},\n    plugins: [],\n    toolbar: {\n        buttons: [\n            _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE\n        ]\n    },\n    labels: {},\n    animation: {\n        fade: 250,\n        swipe: 500,\n        easing: {\n            fade: \"ease\",\n            swipe: \"ease-out\",\n            navigation: \"ease-in-out\"\n        }\n    },\n    carousel: {\n        finite: false,\n        preload: 2,\n        padding: \"16px\",\n        spacing: \"30%\",\n        imageFit: _types_js__WEBPACK_IMPORTED_MODULE_2__.IMAGE_FIT_CONTAIN,\n        imageProps: {}\n    },\n    controller: {\n        ref: null,\n        focus: true,\n        aria: false,\n        touchAction: \"none\",\n        closeOnPullUp: false,\n        closeOnPullDown: false,\n        closeOnBackdropClick: false,\n        preventDefaultWheelX: true,\n        preventDefaultWheelY: false\n    },\n    portal: {},\n    noScroll: {\n        disabled: false\n    },\n    on: {},\n    styles: {},\n    className: \"\"\n};\nfunction createModule(name, component) {\n    return {\n        name,\n        component\n    };\n}\nfunction createNode(module, children) {\n    return {\n        module,\n        children\n    };\n}\nfunction traverseNode(node, target, apply) {\n    if (node.module.name === target) {\n        return apply(node);\n    }\n    if (node.children) {\n        return [\n            createNode(node.module, node.children.flatMap((n)=>{\n                var _a;\n                return (_a = traverseNode(n, target, apply)) !== null && _a !== void 0 ? _a : [];\n            }))\n        ];\n    }\n    return [\n        node\n    ];\n}\nfunction traverse(nodes, target, apply) {\n    return nodes.flatMap((node)=>{\n        var _a;\n        return (_a = traverseNode(node, target, apply)) !== null && _a !== void 0 ? _a : [];\n    });\n}\nfunction withPlugins(root, plugins = [], augmentations = []) {\n    let config = root;\n    const contains = (target)=>{\n        const nodes = [\n            ...config\n        ];\n        while(nodes.length > 0){\n            const node = nodes.pop();\n            if ((node === null || node === void 0 ? void 0 : node.module.name) === target) return true;\n            if (node === null || node === void 0 ? void 0 : node.children) nodes.push(...node.children);\n        }\n        return false;\n    };\n    const addParent = (target, module)=>{\n        if (target === \"\") {\n            config = [\n                createNode(module, config)\n            ];\n            return;\n        }\n        config = traverse(config, target, (node)=>[\n                createNode(module, [\n                    node\n                ])\n            ]);\n    };\n    const append = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(node.module, [\n                    createNode(module, node.children)\n                ])\n            ]);\n    };\n    const addChild = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>{\n            var _a;\n            return [\n                createNode(node.module, [\n                    ...precede ? [\n                        createNode(module)\n                    ] : [],\n                    ...(_a = node.children) !== null && _a !== void 0 ? _a : [],\n                    ...!precede ? [\n                        createNode(module)\n                    ] : []\n                ])\n            ];\n        });\n    };\n    const addSibling = (target, module, precede)=>{\n        config = traverse(config, target, (node)=>[\n                ...precede ? [\n                    createNode(module)\n                ] : [],\n                node,\n                ...!precede ? [\n                    createNode(module)\n                ] : []\n            ]);\n    };\n    const addModule = (module)=>{\n        append(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, module);\n    };\n    const replace = (target, module)=>{\n        config = traverse(config, target, (node)=>[\n                createNode(module, node.children)\n            ]);\n    };\n    const remove = (target)=>{\n        config = traverse(config, target, (node)=>node.children);\n    };\n    const augment = (augmentation)=>{\n        augmentations.push(augmentation);\n    };\n    plugins.forEach((plugin)=>{\n        plugin({\n            contains,\n            addParent,\n            append,\n            addChild,\n            addSibling,\n            addModule,\n            replace,\n            remove,\n            augment\n        });\n    });\n    return {\n        config,\n        augmentation: (props)=>augmentations.reduce((acc, augmentation)=>augmentation(acc), props)\n    };\n}\nconst DocumentContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useDocumentContext = makeUseContext(\"useDocument\", \"DocumentContext\", DocumentContext);\nfunction DocumentContextProvider({ nodeRef, children }) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const getOwnerDocument = (node)=>{\n            var _a;\n            return ((_a = node || nodeRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || document;\n        };\n        const getOwnerWindow = (node)=>{\n            var _a;\n            return ((_a = getOwnerDocument(node)) === null || _a === void 0 ? void 0 : _a.defaultView) || window;\n        };\n        return {\n            getOwnerDocument,\n            getOwnerWindow\n        };\n    }, [\n        nodeRef\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContext.Provider, {\n        value: context\n    }, children);\n}\nconst EventsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useEvents = makeUseContext(\"useEvents\", \"EventsContext\", EventsContext);\nfunction EventsProvider({ children }) {\n    const [subscriptions] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            Object.keys(subscriptions).forEach((topic)=>delete subscriptions[topic]);\n        }, [\n        subscriptions\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const unsubscribe = (topic, callback)=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.splice(0, subscriptions[topic].length, ...subscriptions[topic].filter((cb)=>cb !== callback));\n        };\n        const subscribe = (topic, callback)=>{\n            if (!subscriptions[topic]) {\n                subscriptions[topic] = [];\n            }\n            subscriptions[topic].push(callback);\n            return ()=>unsubscribe(topic, callback);\n        };\n        const publish = (...[topic, event])=>{\n            var _a;\n            (_a = subscriptions[topic]) === null || _a === void 0 ? void 0 : _a.forEach((callback)=>callback(event));\n        };\n        return {\n            publish,\n            subscribe,\n            unsubscribe\n        };\n    }, [\n        subscriptions\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsContext.Provider, {\n        value: context\n    }, children);\n}\nconst LightboxPropsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxProps = makeUseContext(\"useLightboxProps\", \"LightboxPropsContext\", LightboxPropsContext);\nfunction LightboxPropsProvider({ children, ...props }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsContext.Provider, {\n        value: props\n    }, children);\n}\nconst LightboxStateContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxState = makeUseContext(\"useLightboxState\", \"LightboxStateContext\", LightboxStateContext);\nconst LightboxDispatchContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useLightboxDispatch = makeUseContext(\"useLightboxDispatch\", \"LightboxDispatchContext\", LightboxDispatchContext);\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"swipe\":\n            {\n                const { slides } = state;\n                const increment = (action === null || action === void 0 ? void 0 : action.increment) || 0;\n                const globalIndex = state.globalIndex + increment;\n                const currentIndex = getSlideIndex(globalIndex, slides.length);\n                const currentSlide = getSlideIfPresent(slides, currentIndex);\n                const animation = increment || action.duration ? {\n                    increment,\n                    duration: action.duration,\n                    easing: action.easing\n                } : undefined;\n                return {\n                    slides,\n                    currentIndex,\n                    globalIndex,\n                    currentSlide,\n                    animation\n                };\n            }\n        case \"update\":\n            if (action.slides !== state.slides || action.index !== state.currentIndex) {\n                return {\n                    slides: action.slides,\n                    currentIndex: action.index,\n                    globalIndex: action.index,\n                    currentSlide: getSlideIfPresent(action.slides, action.index)\n                };\n            }\n            return state;\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction LightboxStateProvider({ slides, index, children }) {\n    const [state, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {\n        slides,\n        currentIndex: index,\n        globalIndex: index,\n        currentSlide: getSlideIfPresent(slides, index)\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        dispatch({\n            type: \"update\",\n            slides,\n            index\n        });\n    }, [\n        slides,\n        index\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            ...state,\n            state,\n            dispatch\n        }), [\n        state,\n        dispatch\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxDispatchContext.Provider, {\n        value: dispatch\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateContext.Provider, {\n        value: context\n    }, children));\n}\nconst TimeoutsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useTimeouts = makeUseContext(\"useTimeouts\", \"TimeoutsContext\", TimeoutsContext);\nfunction TimeoutsProvider({ children }) {\n    const [timeouts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>{\n            timeouts.forEach((tid)=>window.clearTimeout(tid));\n            timeouts.splice(0, timeouts.length);\n        }, [\n        timeouts\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const removeTimeout = (id)=>{\n            timeouts.splice(0, timeouts.length, ...timeouts.filter((tid)=>tid !== id));\n        };\n        const setTimeout = (fn, delay)=>{\n            const id = window.setTimeout(()=>{\n                removeTimeout(id);\n                fn();\n            }, delay);\n            timeouts.push(id);\n            return id;\n        };\n        const clearTimeout = (id)=>{\n            if (id !== undefined) {\n                removeTimeout(id);\n                window.clearTimeout(id);\n            }\n        };\n        return {\n            setTimeout,\n            clearTimeout\n        };\n    }, [\n        timeouts\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsContext.Provider, {\n        value: context\n    }, children);\n}\nconst IconButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function IconButton({ label: label$1, className, icon: Icon, renderIcon, onClick, style, ...rest }, ref) {\n    const { styles, labels } = useLightboxProps();\n    const buttonLabel = label(labels, label$1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        ref: ref,\n        type: \"button\",\n        title: buttonLabel,\n        \"aria-label\": buttonLabel,\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_BUTTON), className),\n        onClick: onClick,\n        style: {\n            ...style,\n            ...styles.button\n        },\n        ...rest\n    }, renderIcon ? renderIcon() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Icon, {\n        className: cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON),\n        style: styles.icon\n    }));\n});\nfunction svgIcon(name, children) {\n    const icon = (props)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            viewBox: \"0 0 24 24\",\n            width: \"24\",\n            height: \"24\",\n            \"aria-hidden\": \"true\",\n            focusable: \"false\",\n            ...props\n        }, children);\n    icon.displayName = name;\n    return icon;\n}\nfunction createIcon(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph));\n}\nfunction createIconDisabled(name, glyph) {\n    return svgIcon(name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"defs\", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"mask\", {\n        id: \"strike\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"white\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0L24 24\",\n        stroke: \"black\",\n        strokeWidth: 4\n    }))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0.70707 2.121320L21.878680 23.292883\",\n        stroke: \"currentColor\",\n        strokeWidth: 2\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"g\", {\n        fill: \"currentColor\",\n        mask: \"url(#strike)\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"M0 0h24v24H0z\",\n        fill: \"none\"\n    }), glyph)));\n}\nconst CloseIcon = createIcon(\"Close\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"\n}));\nconst PreviousIcon = createIcon(\"Previous\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n}));\nconst NextIcon = createIcon(\"Next\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}));\nconst LoadingIcon = createIcon(\"Loading\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, Array.from({\n    length: 8\n}).map((_, index, array)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        key: index,\n        x1: \"12\",\n        y1: \"6.5\",\n        x2: \"12\",\n        y2: \"1.8\",\n        strokeLinecap: \"round\",\n        strokeWidth: \"2.6\",\n        stroke: \"currentColor\",\n        strokeOpacity: 1 / array.length * (index + 1),\n        transform: `rotate(${360 / array.length * index}, 12, 12)`\n    }))));\nconst ErrorIcon = createIcon(\"Error\", /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    d: \"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z\"\n}));\nconst useLayoutEffect = hasWindow() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMotionPreference() {\n    const [reduceMotion, setReduceMotion] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        const mediaQuery = (_a = window.matchMedia) === null || _a === void 0 ? void 0 : _a.call(window, \"(prefers-reduced-motion: reduce)\");\n        setReduceMotion(mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.matches);\n        const listener = (event)=>setReduceMotion(event.matches);\n        (_b = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.addEventListener) === null || _b === void 0 ? void 0 : _b.call(mediaQuery, \"change\", listener);\n        return ()=>{\n            var _a;\n            return (_a = mediaQuery === null || mediaQuery === void 0 ? void 0 : mediaQuery.removeEventListener) === null || _a === void 0 ? void 0 : _a.call(mediaQuery, \"change\", listener);\n        };\n    }, []);\n    return reduceMotion;\n}\nfunction currentTransformation(node) {\n    let x = 0;\n    let y = 0;\n    let z = 0;\n    const matrix = window.getComputedStyle(node).transform;\n    const matcher = matrix.match(/matrix.*\\((.+)\\)/);\n    if (matcher) {\n        const values = matcher[1].split(\",\").map(parseInt);\n        if (values.length === 6) {\n            x = values[4];\n            y = values[5];\n        } else if (values.length === 16) {\n            x = values[12];\n            y = values[13];\n            z = values[14];\n        }\n    }\n    return {\n        x,\n        y,\n        z\n    };\n}\nfunction useAnimation(nodeRef, computeAnimation) {\n    const snapshot = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const animation = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const reduceMotion = useMotionPreference();\n    useLayoutEffect(()=>{\n        var _a, _b, _c;\n        if (nodeRef.current && snapshot.current !== undefined && !reduceMotion) {\n            const { keyframes, duration, easing, onfinish } = computeAnimation(snapshot.current, nodeRef.current.getBoundingClientRect(), currentTransformation(nodeRef.current)) || {};\n            if (keyframes && duration) {\n                (_a = animation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n                animation.current = undefined;\n                try {\n                    animation.current = (_c = (_b = nodeRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, keyframes, {\n                        duration,\n                        easing\n                    });\n                } catch (err) {\n                    console.error(err);\n                }\n                if (animation.current) {\n                    animation.current.onfinish = ()=>{\n                        animation.current = undefined;\n                        onfinish === null || onfinish === void 0 ? void 0 : onfinish();\n                    };\n                }\n            }\n        }\n        snapshot.current = undefined;\n    });\n    return {\n        prepareAnimation: (currentSnapshot)=>{\n            snapshot.current = currentSnapshot;\n        },\n        isAnimationPlaying: ()=>{\n            var _a;\n            return ((_a = animation.current) === null || _a === void 0 ? void 0 : _a.playState) === \"running\";\n        }\n    };\n}\nfunction useContainerRect() {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const observerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const [containerRect, setContainerRect] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const setContainerRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        containerRef.current = node;\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n            observerRef.current = undefined;\n        }\n        const updateContainerRect = ()=>{\n            if (node) {\n                const styles = window.getComputedStyle(node);\n                const parse = (value)=>parseFloat(value) || 0;\n                setContainerRect({\n                    width: Math.round(node.clientWidth - parse(styles.paddingLeft) - parse(styles.paddingRight)),\n                    height: Math.round(node.clientHeight - parse(styles.paddingTop) - parse(styles.paddingBottom))\n                });\n            } else {\n                setContainerRect(undefined);\n            }\n        };\n        updateContainerRect();\n        if (node && typeof ResizeObserver !== \"undefined\") {\n            observerRef.current = new ResizeObserver(updateContainerRect);\n            observerRef.current.observe(node);\n        }\n    }, []);\n    return {\n        setContainerRef,\n        containerRef,\n        containerRect\n    };\n}\nfunction useDelay() {\n    const timeoutId = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { setTimeout, clearTimeout } = useTimeouts();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((callback, delay)=>{\n        clearTimeout(timeoutId.current);\n        timeoutId.current = setTimeout(callback, delay > 0 ? delay : 0);\n    }, [\n        setTimeout,\n        clearTimeout\n    ]);\n}\nfunction useEventCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useLayoutEffect(()=>{\n        ref.current = fn;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        var _a;\n        return (_a = ref.current) === null || _a === void 0 ? void 0 : _a.call(ref, ...args);\n    }, []);\n}\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref) {\n        ref.current = value;\n    }\n}\nfunction useForkRef(refA, refB) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>refA == null && refB == null ? null : (refValue)=>{\n            setRef(refA, refValue);\n            setRef(refB, refValue);\n        }, [\n        refA,\n        refB\n    ]);\n}\nfunction useLoseFocus(focus, disabled = false) {\n    const focused = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    useLayoutEffect(()=>{\n        if (disabled && focused.current) {\n            focused.current = false;\n            focus();\n        }\n    }, [\n        disabled,\n        focus\n    ]);\n    const onFocus = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = true;\n    }, []);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        focused.current = false;\n    }, []);\n    return {\n        onFocus,\n        onBlur\n    };\n}\nfunction useRTL() {\n    const [isRTL, setIsRTL] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useLayoutEffect(()=>{\n        setIsRTL(window.getComputedStyle(window.document.documentElement).direction === \"rtl\");\n    }, []);\n    return isRTL;\n}\nfunction useSensors() {\n    const [subscribers] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const notifySubscribers = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, event)=>{\n        var _a;\n        (_a = subscribers[type]) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>{\n            if (!event.isPropagationStopped()) listener(event);\n        });\n    }, [\n        subscribers\n    ]);\n    const registerSensors = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            onPointerDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, event),\n            onPointerMove: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, event),\n            onPointerUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, event),\n            onPointerLeave: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, event),\n            onPointerCancel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, event),\n            onKeyDown: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, event),\n            onKeyUp: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_UP, event),\n            onWheel: (event)=>notifySubscribers(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, event)\n        }), [\n        notifySubscribers\n    ]);\n    const subscribeSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((type, callback)=>{\n        if (!subscribers[type]) {\n            subscribers[type] = [];\n        }\n        subscribers[type].unshift(callback);\n        return ()=>{\n            const listeners = subscribers[type];\n            if (listeners) {\n                listeners.splice(0, listeners.length, ...listeners.filter((el)=>el !== callback));\n            }\n        };\n    }, [\n        subscribers\n    ]);\n    return {\n        registerSensors,\n        subscribeSensors\n    };\n}\nfunction useThrottle(callback, delay) {\n    const lastCallbackTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const delayCallback = useDelay();\n    const executeCallback = useEventCallback((...args)=>{\n        lastCallbackTime.current = Date.now();\n        callback(args);\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        delayCallback(()=>{\n            executeCallback(args);\n        }, delay - (Date.now() - lastCallbackTime.current));\n    }, [\n        delay,\n        executeCallback,\n        delayCallback\n    ]);\n}\nconst slidePrefix = makeComposePrefix(\"slide\");\nconst slideImagePrefix = makeComposePrefix(\"slide_image\");\nfunction ImageSlide({ slide: image, offset, render, rect, imageFit, imageProps, onClick, onLoad, onError, style }) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const [status, setStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING);\n    const { publish } = useEvents();\n    const { setTimeout } = useTimeouts();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (offset === 0) {\n            publish((0,_types_js__WEBPACK_IMPORTED_MODULE_2__.activeSlideStatus)(status));\n        }\n    }, [\n        offset,\n        status,\n        publish\n    ]);\n    const handleLoading = useEventCallback((img)=>{\n        (\"decode\" in img ? img.decode() : Promise.resolve()).catch(()=>{}).then(()=>{\n            if (!img.parentNode) {\n                return;\n            }\n            setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE);\n            setTimeout(()=>{\n                onLoad === null || onLoad === void 0 ? void 0 : onLoad(img);\n            }, 0);\n        });\n    });\n    const setImageRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((img)=>{\n        imageRef.current = img;\n        if (img === null || img === void 0 ? void 0 : img.complete) {\n            handleLoading(img);\n        }\n    }, [\n        handleLoading\n    ]);\n    const handleOnLoad = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        handleLoading(event.currentTarget);\n    }, [\n        handleLoading\n    ]);\n    const handleOnError = useEventCallback(()=>{\n        setStatus(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR);\n        onError === null || onError === void 0 ? void 0 : onError();\n    });\n    const cover = isImageFitCover(image, imageFit);\n    const nonInfinite = (value, fallback)=>Number.isFinite(value) ? value : fallback;\n    const maxWidth = nonInfinite(Math.max(...((_b = (_a = image.srcSet) === null || _a === void 0 ? void 0 : _a.map((x)=>x.width)) !== null && _b !== void 0 ? _b : []).concat(image.width ? [\n        image.width\n    ] : []).filter(Boolean)), ((_c = imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalWidth) || 0);\n    const maxHeight = nonInfinite(Math.max(...((_e = (_d = image.srcSet) === null || _d === void 0 ? void 0 : _d.map((x)=>x.height)) !== null && _e !== void 0 ? _e : []).concat(image.height ? [\n        image.height\n    ] : []).filter(Boolean)), ((_f = imageRef.current) === null || _f === void 0 ? void 0 : _f.naturalHeight) || 0);\n    const defaultStyle = maxWidth && maxHeight ? {\n        maxWidth: `min(${maxWidth}px, 100%)`,\n        maxHeight: `min(${maxHeight}px, 100%)`\n    } : {\n        maxWidth: \"100%\",\n        maxHeight: \"100%\"\n    };\n    const srcSet = (_g = image.srcSet) === null || _g === void 0 ? void 0 : _g.sort((a, b)=>a.width - b.width).map((item)=>`${item.src} ${item.width}w`).join(\", \");\n    const estimateActualWidth = ()=>rect && !cover && image.width && image.height ? rect.height / image.height * image.width : Number.MAX_VALUE;\n    const sizes = srcSet && rect && hasWindow() ? `${Math.round(Math.min(estimateActualWidth(), rect.width))}px` : undefined;\n    const { style: imagePropsStyle, className: imagePropsClassName, ...restImageProps } = imageProps || {};\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n        ref: setImageRef,\n        onLoad: handleOnLoad,\n        onError: handleOnError,\n        onClick: onClick,\n        draggable: false,\n        className: clsx(cssClass(slideImagePrefix()), cover && cssClass(slideImagePrefix(\"cover\")), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && cssClass(slideImagePrefix(\"loading\")), imagePropsClassName),\n        style: {\n            ...defaultStyle,\n            ...style,\n            ...imagePropsStyle\n        },\n        ...restImageProps,\n        alt: image.alt,\n        sizes: sizes,\n        srcSet: srcSet,\n        src: image.src\n    }), status !== _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_COMPLETE && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_PLACEHOLDER))\n    }, status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING && ((render === null || render === void 0 ? void 0 : render.iconLoading) ? render.iconLoading() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LoadingIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_LOADING)))\n    })), status === _types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR && ((render === null || render === void 0 ? void 0 : render.iconError) ? render.iconError() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ErrorIcon, {\n        className: clsx(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.ELEMENT_ICON), cssClass(slidePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.SLIDE_STATUS_ERROR)))\n    }))));\n}\nconst LightboxRoot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function LightboxRoot({ className, children, ...rest }, ref) {\n    const nodeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DocumentContextProvider, {\n        nodeRef: nodeRef\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: useForkRef(ref, nodeRef),\n        className: clsx(cssClass(\"root\"), className),\n        ...rest\n    }, children));\n});\nvar SwipeState;\n(function(SwipeState) {\n    SwipeState[SwipeState[\"NONE\"] = 0] = \"NONE\";\n    SwipeState[SwipeState[\"SWIPE\"] = 1] = \"SWIPE\";\n    SwipeState[SwipeState[\"PULL\"] = 2] = \"PULL\";\n    SwipeState[SwipeState[\"ANIMATION\"] = 3] = \"ANIMATION\";\n})(SwipeState || (SwipeState = {}));\nfunction usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled) {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>!disabled ? cleanup(subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_DOWN, onPointerDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_MOVE, onPointerMove), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_UP, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_LEAVE, onPointerUp), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_POINTER_CANCEL, onPointerUp)) : ()=>{}, [\n        subscribeSensors,\n        onPointerDown,\n        onPointerMove,\n        onPointerUp,\n        disabled\n    ]);\n}\nvar Gesture;\n(function(Gesture) {\n    Gesture[Gesture[\"NONE\"] = 0] = \"NONE\";\n    Gesture[Gesture[\"SWIPE\"] = 1] = \"SWIPE\";\n    Gesture[Gesture[\"PULL\"] = 2] = \"PULL\";\n})(Gesture || (Gesture = {}));\nconst SWIPE_THRESHOLD = 30;\nfunction usePointerSwipe(subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel, pullUpEnabled, pullDownEnabled, onPullStart, onPullProgress, onPullFinish, onPullCancel) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const activePointer = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const gesture = react__WEBPACK_IMPORTED_MODULE_0__.useRef(Gesture.NONE);\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (activePointer.current === event.pointerId) {\n            activePointer.current = undefined;\n            gesture.current = Gesture.NONE;\n        }\n        const currentPointers = pointers.current;\n        currentPointers.splice(0, currentPointers.length, ...currentPointers.filter((p)=>p.pointerId !== event.pointerId));\n    }, []);\n    const addPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        clearPointer(event);\n        event.persist();\n        pointers.current.push(event);\n    }, [\n        clearPointer\n    ]);\n    const onPointerDown = useEventCallback((event)=>{\n        addPointer(event);\n    });\n    const exceedsPullThreshold = (value, threshold)=>pullDownEnabled && value > threshold || pullUpEnabled && value < -threshold;\n    const onPointerUp = useEventCallback((event)=>{\n        if (pointers.current.find((x)=>x.pointerId === event.pointerId) && activePointer.current === event.pointerId) {\n            const duration = Date.now() - startTime.current;\n            const currentOffset = offset.current;\n            if (gesture.current === Gesture.SWIPE) {\n                if (Math.abs(currentOffset) > 0.3 * containerWidth || Math.abs(currentOffset) > 5 && duration < swipeAnimationDuration) {\n                    onSwipeFinish(currentOffset, duration);\n                } else {\n                    onSwipeCancel(currentOffset);\n                }\n            } else if (gesture.current === Gesture.PULL) {\n                if (exceedsPullThreshold(currentOffset, 2 * SWIPE_THRESHOLD)) {\n                    onPullFinish(currentOffset, duration);\n                } else {\n                    onPullCancel(currentOffset);\n                }\n            }\n            offset.current = 0;\n            gesture.current = Gesture.NONE;\n        }\n        clearPointer(event);\n    });\n    const onPointerMove = useEventCallback((event)=>{\n        const pointer = pointers.current.find((p)=>p.pointerId === event.pointerId);\n        if (pointer) {\n            const isCurrentPointer = activePointer.current === event.pointerId;\n            if (event.buttons === 0) {\n                if (isCurrentPointer && offset.current !== 0) {\n                    onPointerUp(event);\n                } else {\n                    clearPointer(pointer);\n                }\n                return;\n            }\n            const deltaX = event.clientX - pointer.clientX;\n            const deltaY = event.clientY - pointer.clientY;\n            if (activePointer.current === undefined) {\n                const startGesture = (newGesture)=>{\n                    addPointer(event);\n                    activePointer.current = event.pointerId;\n                    startTime.current = Date.now();\n                    gesture.current = newGesture;\n                };\n                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > SWIPE_THRESHOLD && isSwipeValid(deltaX)) {\n                    startGesture(Gesture.SWIPE);\n                    onSwipeStart();\n                } else if (Math.abs(deltaY) > Math.abs(deltaX) && exceedsPullThreshold(deltaY, SWIPE_THRESHOLD)) {\n                    startGesture(Gesture.PULL);\n                    onPullStart();\n                }\n            } else if (isCurrentPointer) {\n                if (gesture.current === Gesture.SWIPE) {\n                    offset.current = deltaX;\n                    onSwipeProgress(deltaX);\n                } else if (gesture.current === Gesture.PULL) {\n                    offset.current = deltaY;\n                    onPullProgress(deltaY);\n                }\n            }\n        }\n    });\n    usePointerEvents(subscribeSensors, onPointerDown, onPointerMove, onPointerUp);\n}\nfunction usePreventWheelDefaults({ preventDefaultWheelX, preventDefaultWheelY }) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const listener = useEventCallback((event)=>{\n        const horizontal = Math.abs(event.deltaX) > Math.abs(event.deltaY);\n        if (horizontal && preventDefaultWheelX || !horizontal && preventDefaultWheelY || event.ctrlKey) {\n            event.preventDefault();\n        }\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        var _a;\n        if (node) {\n            node.addEventListener(\"wheel\", listener, {\n                passive: false\n            });\n        } else {\n            (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(\"wheel\", listener);\n        }\n        ref.current = node;\n    }, [\n        listener\n    ]);\n}\nfunction useWheelSwipe(swipeState, subscribeSensors, isSwipeValid, containerWidth, swipeAnimationDuration, onSwipeStart, onSwipeProgress, onSwipeFinish, onSwipeCancel) {\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intent = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const intentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const resetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const wheelInertia = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const wheelInertiaCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const startTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { setTimeout, clearTimeout } = useTimeouts();\n    const cancelSwipeIntentCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (intentCleanup.current) {\n            clearTimeout(intentCleanup.current);\n            intentCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const cancelSwipeResetCleanup = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (resetCleanup.current) {\n            clearTimeout(resetCleanup.current);\n            resetCleanup.current = undefined;\n        }\n    }, [\n        clearTimeout\n    ]);\n    const handleCleanup = useEventCallback(()=>{\n        if (swipeState !== SwipeState.SWIPE) {\n            offset.current = 0;\n            startTime.current = 0;\n            cancelSwipeIntentCleanup();\n            cancelSwipeResetCleanup();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleCleanup, [\n        swipeState,\n        handleCleanup\n    ]);\n    const handleCancelSwipe = useEventCallback((currentSwipeOffset)=>{\n        resetCleanup.current = undefined;\n        if (offset.current === currentSwipeOffset) {\n            onSwipeCancel(offset.current);\n        }\n    });\n    const onWheel = useEventCallback((event)=>{\n        if (event.ctrlKey) {\n            return;\n        }\n        if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n            return;\n        }\n        const setWheelInertia = (inertia)=>{\n            wheelInertia.current = inertia;\n            clearTimeout(wheelInertiaCleanup.current);\n            wheelInertiaCleanup.current = inertia > 0 ? setTimeout(()=>{\n                wheelInertia.current = 0;\n                wheelInertiaCleanup.current = undefined;\n            }, 300) : undefined;\n        };\n        if (swipeState === SwipeState.NONE) {\n            if (Math.abs(event.deltaX) <= 1.2 * Math.abs(wheelInertia.current)) {\n                setWheelInertia(event.deltaX);\n                return;\n            }\n            if (!isSwipeValid(-event.deltaX)) {\n                return;\n            }\n            intent.current += event.deltaX;\n            cancelSwipeIntentCleanup();\n            if (Math.abs(intent.current) > 30) {\n                intent.current = 0;\n                setWheelInertia(0);\n                startTime.current = Date.now();\n                onSwipeStart();\n            } else {\n                const currentSwipeIntent = intent.current;\n                intentCleanup.current = setTimeout(()=>{\n                    intentCleanup.current = undefined;\n                    if (currentSwipeIntent === intent.current) {\n                        intent.current = 0;\n                    }\n                }, swipeAnimationDuration);\n            }\n        } else if (swipeState === SwipeState.SWIPE) {\n            let newSwipeOffset = offset.current - event.deltaX;\n            newSwipeOffset = Math.min(Math.abs(newSwipeOffset), containerWidth) * Math.sign(newSwipeOffset);\n            offset.current = newSwipeOffset;\n            onSwipeProgress(newSwipeOffset);\n            cancelSwipeResetCleanup();\n            if (Math.abs(newSwipeOffset) > 0.2 * containerWidth) {\n                setWheelInertia(event.deltaX);\n                onSwipeFinish(newSwipeOffset, Date.now() - startTime.current);\n                return;\n            }\n            resetCleanup.current = setTimeout(()=>handleCancelSwipe(newSwipeOffset), 2 * swipeAnimationDuration);\n        } else {\n            setWheelInertia(event.deltaX);\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel), [\n        subscribeSensors,\n        onWheel\n    ]);\n}\nconst cssContainerPrefix = makeComposePrefix(\"container\");\nconst ControllerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useController = makeUseContext(\"useController\", \"ControllerContext\", ControllerContext);\nfunction Controller({ children, ...props }) {\n    var _a;\n    const { carousel, animation, controller, on, styles, render } = props;\n    const { closeOnPullUp, closeOnPullDown, preventDefaultWheelX, preventDefaultWheelY } = controller;\n    const [toolbarWidth, setToolbarWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const state = useLightboxState();\n    const dispatch = useLightboxDispatch();\n    const [swipeState, setSwipeState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(SwipeState.NONE);\n    const swipeOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOffset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pullOpacity = react__WEBPACK_IMPORTED_MODULE_0__.useRef(1);\n    const { registerSensors, subscribeSensors } = useSensors();\n    const { subscribe, publish } = useEvents();\n    const cleanupAnimationIncrement = useDelay();\n    const cleanupSwipeOffset = useDelay();\n    const cleanupPullOffset = useDelay();\n    const { containerRef, setContainerRef, containerRect } = useContainerRect();\n    const handleContainerRef = useForkRef(usePreventWheelDefaults({\n        preventDefaultWheelX,\n        preventDefaultWheelY\n    }), setContainerRef);\n    const carouselRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const setCarouselRef = useForkRef(carouselRef, undefined);\n    const { getOwnerDocument } = useDocumentContext();\n    const isRTL = useRTL();\n    const rtl = (value)=>(isRTL ? -1 : 1) * (typeof value === \"number\" ? value : 1);\n    const focus = useEventCallback(()=>{\n        var _a;\n        return (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    });\n    const getLightboxProps = useEventCallback(()=>props);\n    const getLightboxState = useEventCallback(()=>state);\n    const prev = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, params), [\n        publish\n    ]);\n    const next = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((params)=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, params), [\n        publish\n    ]);\n    const close = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE), [\n        publish\n    ]);\n    const isSwipeValid = (offset)=>!(carousel.finite && (rtl(offset) > 0 && state.currentIndex === 0 || rtl(offset) < 0 && state.currentIndex === state.slides.length - 1));\n    const setSwipeOffset = (offset)=>{\n        var _a;\n        swipeOffset.current = offset;\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"swipe_offset\"), `${Math.round(offset)}px`);\n    };\n    const setPullOffset = (offset)=>{\n        var _a, _b;\n        pullOffset.current = offset;\n        pullOpacity.current = (()=>{\n            const threshold = 60;\n            const minOpacity = 0.5;\n            const offsetValue = (()=>{\n                if (closeOnPullDown && offset > 0) return offset;\n                if (closeOnPullUp && offset < 0) return -offset;\n                return 0;\n            })();\n            return Math.min(Math.max(round(1 - offsetValue / threshold * (1 - minOpacity), 2), minOpacity), 1);\n        })();\n        (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.style.setProperty(cssVar(\"pull_offset\"), `${Math.round(offset)}px`);\n        (_b = containerRef.current) === null || _b === void 0 ? void 0 : _b.style.setProperty(cssVar(\"pull_opacity\"), `${pullOpacity.current}`);\n    };\n    const { prepareAnimation: preparePullAnimation } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        if (carouselRef.current && containerRect) {\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(0, ${snapshot.rect.y - rect.y + translate.y}px)`,\n                        opacity: snapshot.opacity\n                    },\n                    {\n                        transform: \"translate(0, 0)\",\n                        opacity: 1\n                    }\n                ],\n                duration: snapshot.duration,\n                easing: animation.easing.fade\n            };\n        }\n        return undefined;\n    });\n    const pull = (offset, cancel)=>{\n        if (closeOnPullUp || closeOnPullDown) {\n            setPullOffset(offset);\n            let duration = 0;\n            if (carouselRef.current) {\n                duration = animation.fade * (cancel ? 2 : 1);\n                preparePullAnimation({\n                    rect: carouselRef.current.getBoundingClientRect(),\n                    opacity: pullOpacity.current,\n                    duration\n                });\n            }\n            cleanupPullOffset(()=>{\n                setPullOffset(0);\n                setSwipeState(SwipeState.NONE);\n            }, duration);\n            setSwipeState(SwipeState.ANIMATION);\n            if (!cancel) {\n                close();\n            }\n        }\n    };\n    const { prepareAnimation, isAnimationPlaying } = useAnimation(carouselRef, (snapshot, rect, translate)=>{\n        var _a;\n        if (carouselRef.current && containerRect && ((_a = state.animation) === null || _a === void 0 ? void 0 : _a.duration)) {\n            const parsedSpacing = parseLengthPercentage(carousel.spacing);\n            const spacingValue = (parsedSpacing.percent ? parsedSpacing.percent * containerRect.width / 100 : parsedSpacing.pixel) || 0;\n            return {\n                keyframes: [\n                    {\n                        transform: `translate(${rtl(state.globalIndex - snapshot.index) * (containerRect.width + spacingValue) + snapshot.rect.x - rect.x + translate.x}px, 0)`\n                    },\n                    {\n                        transform: \"translate(0, 0)\"\n                    }\n                ],\n                duration: state.animation.duration,\n                easing: state.animation.easing\n            };\n        }\n        return undefined;\n    });\n    const swipe = useEventCallback((action)=>{\n        var _a, _b;\n        const currentSwipeOffset = action.offset || 0;\n        const swipeDuration = !currentSwipeOffset ? (_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe : animation.swipe;\n        const swipeEasing = !currentSwipeOffset && !isAnimationPlaying() ? animation.easing.navigation : animation.easing.swipe;\n        let { direction } = action;\n        const count = (_b = action.count) !== null && _b !== void 0 ? _b : 1;\n        let newSwipeState = SwipeState.ANIMATION;\n        let newSwipeAnimationDuration = swipeDuration * count;\n        if (!direction) {\n            const containerWidth = containerRect === null || containerRect === void 0 ? void 0 : containerRect.width;\n            const elapsedTime = action.duration || 0;\n            const expectedTime = containerWidth ? swipeDuration / containerWidth * Math.abs(currentSwipeOffset) : swipeDuration;\n            if (count !== 0) {\n                if (elapsedTime < expectedTime) {\n                    newSwipeAnimationDuration = newSwipeAnimationDuration / expectedTime * Math.max(elapsedTime, expectedTime / 5);\n                } else if (containerWidth) {\n                    newSwipeAnimationDuration = swipeDuration / containerWidth * (containerWidth - Math.abs(currentSwipeOffset));\n                }\n                direction = rtl(currentSwipeOffset) > 0 ? _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV : _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT;\n            } else {\n                newSwipeAnimationDuration = swipeDuration / 2;\n            }\n        }\n        let increment = 0;\n        if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV) {\n            if (isSwipeValid(rtl(1))) {\n                increment = -count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        } else if (direction === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT) {\n            if (isSwipeValid(rtl(-1))) {\n                increment = count;\n            } else {\n                newSwipeState = SwipeState.NONE;\n                newSwipeAnimationDuration = swipeDuration;\n            }\n        }\n        newSwipeAnimationDuration = Math.round(newSwipeAnimationDuration);\n        cleanupSwipeOffset(()=>{\n            setSwipeOffset(0);\n            setSwipeState(SwipeState.NONE);\n        }, newSwipeAnimationDuration);\n        if (carouselRef.current) {\n            prepareAnimation({\n                rect: carouselRef.current.getBoundingClientRect(),\n                index: state.globalIndex\n            });\n        }\n        setSwipeState(newSwipeState);\n        publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, {\n            type: \"swipe\",\n            increment,\n            duration: newSwipeAnimationDuration,\n            easing: swipeEasing\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a, _b;\n        if (((_a = state.animation) === null || _a === void 0 ? void 0 : _a.increment) && ((_b = state.animation) === null || _b === void 0 ? void 0 : _b.duration)) {\n            cleanupAnimationIncrement(()=>dispatch({\n                    type: \"swipe\",\n                    increment: 0\n                }), state.animation.duration);\n        }\n    }, [\n        state.animation,\n        dispatch,\n        cleanupAnimationIncrement\n    ]);\n    const swipeParams = [\n        subscribeSensors,\n        isSwipeValid,\n        (containerRect === null || containerRect === void 0 ? void 0 : containerRect.width) || 0,\n        animation.swipe,\n        ()=>setSwipeState(SwipeState.SWIPE),\n        (offset)=>setSwipeOffset(offset),\n        (offset, duration)=>swipe({\n                offset,\n                duration,\n                count: 1\n            }),\n        (offset)=>swipe({\n                offset,\n                count: 0\n            })\n    ];\n    const pullParams = [\n        ()=>{\n            if (closeOnPullDown) {\n                setSwipeState(SwipeState.PULL);\n            }\n        },\n        (offset)=>setPullOffset(offset),\n        (offset)=>pull(offset),\n        (offset)=>pull(offset, true)\n    ];\n    usePointerSwipe(...swipeParams, closeOnPullUp, closeOnPullDown, ...pullParams);\n    useWheelSwipe(swipeState, ...swipeParams);\n    const focusOnMount = useEventCallback(()=>{\n        if (controller.focus && getOwnerDocument().querySelector(`.${cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL)} .${cssClass(cssContainerPrefix())}`)) {\n            focus();\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(focusOnMount, [\n        focusOnMount\n    ]);\n    const onViewCallback = useEventCallback(()=>{\n        var _a;\n        (_a = on.view) === null || _a === void 0 ? void 0 : _a.call(on, {\n            index: state.currentIndex\n        });\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onViewCallback, [\n        state.globalIndex,\n        onViewCallback\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>cleanup(subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT, (action)=>swipe({\n                direction: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n                ...action\n            })), subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_SWIPE, (action)=>dispatch(action))), [\n        subscribe,\n        swipe,\n        dispatch\n    ]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            prev,\n            next,\n            close,\n            focus,\n            slideRect: containerRect ? computeSlideRect(containerRect, carousel.padding) : {\n                width: 0,\n                height: 0\n            },\n            containerRect: containerRect || {\n                width: 0,\n                height: 0\n            },\n            subscribeSensors,\n            containerRef,\n            setCarouselRef,\n            toolbarWidth,\n            setToolbarWidth\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        subscribeSensors,\n        containerRect,\n        containerRef,\n        setCarouselRef,\n        toolbarWidth,\n        setToolbarWidth,\n        carousel.padding\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(controller.ref, ()=>({\n            prev,\n            next,\n            close,\n            focus,\n            getLightboxProps,\n            getLightboxState\n        }), [\n        prev,\n        next,\n        close,\n        focus,\n        getLightboxProps,\n        getLightboxState\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: handleContainerRef,\n        className: clsx(cssClass(cssContainerPrefix()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        style: {\n            ...swipeState === SwipeState.SWIPE ? {\n                [cssVar(\"swipe_offset\")]: `${Math.round(swipeOffset.current)}px`\n            } : null,\n            ...swipeState === SwipeState.PULL ? {\n                [cssVar(\"pull_offset\")]: `${Math.round(pullOffset.current)}px`,\n                [cssVar(\"pull_opacity\")]: `${pullOpacity.current}`\n            } : null,\n            ...controller.touchAction !== \"none\" ? {\n                [cssVar(\"controller_touch_action\")]: controller.touchAction\n            } : null,\n            ...styles.container\n        },\n        ...controller.aria ? {\n            role: \"presentation\",\n            \"aria-live\": \"polite\"\n        } : null,\n        tabIndex: -1,\n        ...registerSensors\n    }, containerRect && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ControllerContext.Provider, {\n        value: context\n    }, children, (_a = render.controls) === null || _a === void 0 ? void 0 : _a.call(render)));\n}\nconst ControllerModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, Controller);\nfunction cssPrefix$2(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, value);\n}\nfunction cssSlidePrefix(value) {\n    return composePrefix(\"slide\", value);\n}\nfunction CarouselSlide({ slide, offset }) {\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { currentIndex } = useLightboxState();\n    const { slideRect, close, focus } = useController();\n    const { render, carousel: { imageFit, imageProps }, on: { click: onClick }, controller: { closeOnBackdropClick }, styles: { slide: style } } = useLightboxProps();\n    const { getOwnerDocument } = useDocumentContext();\n    const offscreen = offset !== 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _a;\n        if (offscreen && ((_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.contains(getOwnerDocument().activeElement))) {\n            focus();\n        }\n    }, [\n        offscreen,\n        focus,\n        getOwnerDocument\n    ]);\n    const renderSlide = ()=>{\n        var _a, _b, _c, _d;\n        let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, {\n            slide,\n            offset,\n            rect: slideRect\n        });\n        if (!rendered && isImageSlide(slide)) {\n            rendered = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ImageSlide, {\n                slide: slide,\n                offset: offset,\n                render: render,\n                rect: slideRect,\n                imageFit: imageFit,\n                imageProps: imageProps,\n                onClick: !offscreen ? ()=>onClick === null || onClick === void 0 ? void 0 : onClick({\n                        index: currentIndex\n                    }) : undefined\n            });\n        }\n        return rendered ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (_b = render.slideHeader) === null || _b === void 0 ? void 0 : _b.call(render, {\n            slide\n        }), ((_c = render.slideContainer) !== null && _c !== void 0 ? _c : ({ children })=>children)({\n            slide,\n            children: rendered\n        }), (_d = render.slideFooter) === null || _d === void 0 ? void 0 : _d.call(render, {\n            slide\n        })) : null;\n    };\n    const handleBackdropClick = (event)=>{\n        const container = containerRef.current;\n        const target = event.target instanceof HTMLElement ? event.target : undefined;\n        if (closeOnBackdropClick && target && container && (target === container || Array.from(container.children).find((x)=>x === target) && target.classList.contains(cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER)))) {\n            close();\n        }\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: containerRef,\n        className: clsx(cssClass(cssSlidePrefix()), !offscreen && cssClass(cssSlidePrefix(\"current\")), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER)),\n        ...makeInertWhen(offscreen),\n        onClick: handleBackdropClick,\n        style: style\n    }, renderSlide());\n}\nfunction Placeholder() {\n    const style = useLightboxProps().styles.slide;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: cssClass(\"slide\"),\n        style: style\n    });\n}\nfunction Carousel({ carousel }) {\n    const { slides, currentIndex, globalIndex } = useLightboxState();\n    const { setCarouselRef } = useController();\n    const spacingValue = parseLengthPercentage(carousel.spacing);\n    const paddingValue = parseLengthPercentage(carousel.padding);\n    const preload = calculatePreload(carousel, slides, 1);\n    const items = [];\n    if (hasSlides(slides)) {\n        for(let index = currentIndex - preload; index <= currentIndex + preload; index += 1){\n            const slide = getSlide(slides, index);\n            const key = globalIndex - currentIndex + index;\n            const placeholder = carousel.finite && (index < 0 || index > slides.length - 1);\n            items.push(!placeholder ? {\n                key: [\n                    `${key}`,\n                    getSlideKey(slide)\n                ].filter(Boolean).join(\"|\"),\n                offset: index - currentIndex,\n                slide\n            } : {\n                key\n            });\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setCarouselRef,\n        className: clsx(cssClass(cssPrefix$2()), items.length > 0 && cssClass(cssPrefix$2(\"with_slides\"))),\n        style: {\n            [`${cssVar(cssPrefix$2(\"slides_count\"))}`]: items.length,\n            [`${cssVar(cssPrefix$2(\"spacing_px\"))}`]: spacingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"spacing_percent\"))}`]: spacingValue.percent || 0,\n            [`${cssVar(cssPrefix$2(\"padding_px\"))}`]: paddingValue.pixel || 0,\n            [`${cssVar(cssPrefix$2(\"padding_percent\"))}`]: paddingValue.percent || 0\n        }\n    }, items.map(({ key, slide, offset })=>slide ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CarouselSlide, {\n            key: key,\n            slide: slide,\n            offset: offset\n        }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Placeholder, {\n            key: key\n        })));\n}\nconst CarouselModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CAROUSEL, Carousel);\nfunction useNavigationState() {\n    const { carousel } = useLightboxProps();\n    const { slides, currentIndex } = useLightboxState();\n    const prevDisabled = slides.length === 0 || carousel.finite && currentIndex === 0;\n    const nextDisabled = slides.length === 0 || carousel.finite && currentIndex === slides.length - 1;\n    return {\n        prevDisabled,\n        nextDisabled\n    };\n}\nfunction useKeyboardNavigation(subscribeSensors) {\n    var _a;\n    const isRTL = useRTL();\n    const { publish } = useEvents();\n    const { animation } = useLightboxProps();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    const throttle = ((_a = animation.navigation) !== null && _a !== void 0 ? _a : animation.swipe) / 2;\n    const prev = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV), throttle);\n    const next = useThrottle(()=>publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT), throttle);\n    const handleKeyDown = useEventCallback((event)=>{\n        switch(event.key){\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ESCAPE:\n                publish(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE);\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_LEFT:\n                if (!(isRTL ? nextDisabled : prevDisabled)) (isRTL ? next : prev)();\n                break;\n            case _types_js__WEBPACK_IMPORTED_MODULE_2__.VK_ARROW_RIGHT:\n                if (!(isRTL ? prevDisabled : nextDisabled)) (isRTL ? prev : next)();\n                break;\n            default:\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, handleKeyDown), [\n        subscribeSensors,\n        handleKeyDown\n    ]);\n}\nfunction NavigationButton({ label, icon, renderIcon, action, onClick, disabled, style }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n        label: label,\n        icon: icon,\n        renderIcon: renderIcon,\n        className: cssClass(`navigation_${action}`),\n        disabled: disabled,\n        onClick: onClick,\n        style: style,\n        ...useLoseFocus(useController().focus, disabled)\n    });\n}\nfunction Navigation({ render: { buttonPrev, buttonNext, iconPrev, iconNext }, styles }) {\n    const { prev, next, subscribeSensors } = useController();\n    const { prevDisabled, nextDisabled } = useNavigationState();\n    useKeyboardNavigation(subscribeSensors);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, buttonPrev ? buttonPrev() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Previous\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_PREV,\n        icon: PreviousIcon,\n        renderIcon: iconPrev,\n        style: styles.navigationPrev,\n        disabled: prevDisabled,\n        onClick: prev\n    }), buttonNext ? buttonNext() : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationButton, {\n        label: \"Next\",\n        action: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_NEXT,\n        icon: NextIcon,\n        renderIcon: iconNext,\n        style: styles.navigationNext,\n        disabled: nextDisabled,\n        onClick: next\n    }));\n}\nconst NavigationModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NAVIGATION, Navigation);\nconst noScroll = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL);\nconst noScrollPadding = cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING);\nfunction isHTMLElement(element) {\n    return \"style\" in element;\n}\nfunction padScrollbar(element, padding, rtl) {\n    const styles = window.getComputedStyle(element);\n    const property = rtl ? \"padding-left\" : \"padding-right\";\n    const computedValue = rtl ? styles.paddingLeft : styles.paddingRight;\n    const originalValue = element.style.getPropertyValue(property);\n    element.style.setProperty(property, `${(parseInt(computedValue) || 0) + padding}px`);\n    return ()=>{\n        if (originalValue) {\n            element.style.setProperty(property, originalValue);\n        } else {\n            element.style.removeProperty(property);\n        }\n    };\n}\nfunction NoScroll({ noScroll: { disabled }, children }) {\n    const rtl = useRTL();\n    const { getOwnerDocument, getOwnerWindow } = useDocumentContext();\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (disabled) return ()=>{};\n        const cleanup = [];\n        const ownerWindow = getOwnerWindow();\n        const { body, documentElement } = getOwnerDocument();\n        const scrollbar = Math.round(ownerWindow.innerWidth - documentElement.clientWidth);\n        if (scrollbar > 0) {\n            cleanup.push(padScrollbar(body, scrollbar, rtl));\n            const elements = body.getElementsByTagName(\"*\");\n            for(let i = 0; i < elements.length; i += 1){\n                const element = elements[i];\n                if (isHTMLElement(element) && ownerWindow.getComputedStyle(element).getPropertyValue(\"position\") === \"fixed\" && !element.classList.contains(noScrollPadding)) {\n                    cleanup.push(padScrollbar(element, scrollbar, rtl));\n                }\n            }\n        }\n        body.classList.add(noScroll);\n        return ()=>{\n            body.classList.remove(noScroll);\n            cleanup.forEach((clean)=>clean());\n        };\n    }, [\n        rtl,\n        disabled,\n        getOwnerDocument,\n        getOwnerWindow\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst NoScrollModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_NO_SCROLL, NoScroll);\nfunction cssPrefix$1(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, value);\n}\nfunction setAttribute(element, attribute, value) {\n    const previousValue = element.getAttribute(attribute);\n    element.setAttribute(attribute, value);\n    return ()=>{\n        if (previousValue) {\n            element.setAttribute(attribute, previousValue);\n        } else {\n            element.removeAttribute(attribute);\n        }\n    };\n}\nfunction Portal({ children, animation, styles, className, on, portal, close }) {\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const cleanup = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const restoreFocus = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { setTimeout } = useTimeouts();\n    const { subscribe } = useEvents();\n    const reduceMotion = useMotionPreference();\n    const animationDuration = !reduceMotion ? animation.fade : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n        return ()=>{\n            setMounted(false);\n            setVisible(false);\n        };\n    }, []);\n    const handleCleanup = useEventCallback(()=>{\n        cleanup.current.forEach((clean)=>clean());\n        cleanup.current = [];\n    });\n    const handleClose = useEventCallback(()=>{\n        var _a;\n        setVisible(false);\n        handleCleanup();\n        (_a = on.exiting) === null || _a === void 0 ? void 0 : _a.call(on);\n        setTimeout(()=>{\n            var _a;\n            (_a = on.exited) === null || _a === void 0 ? void 0 : _a.call(on);\n            close();\n        }, animationDuration);\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>subscribe(_types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE, handleClose), [\n        subscribe,\n        handleClose\n    ]);\n    const handleEnter = useEventCallback((node)=>{\n        var _a, _b, _c;\n        node.scrollTop;\n        setVisible(true);\n        (_a = on.entering) === null || _a === void 0 ? void 0 : _a.call(on);\n        const elements = (_c = (_b = node.parentNode) === null || _b === void 0 ? void 0 : _b.children) !== null && _c !== void 0 ? _c : [];\n        for(let i = 0; i < elements.length; i += 1){\n            const element = elements[i];\n            if ([\n                \"TEMPLATE\",\n                \"SCRIPT\",\n                \"STYLE\"\n            ].indexOf(element.tagName) === -1 && element !== node) {\n                cleanup.current.push(setAttribute(element, \"inert\", \"\"));\n                cleanup.current.push(setAttribute(element, \"aria-hidden\", \"true\"));\n            }\n        }\n        cleanup.current.push(()=>{\n            var _a, _b;\n            (_b = (_a = restoreFocus.current) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);\n        });\n        setTimeout(()=>{\n            var _a;\n            (_a = on.entered) === null || _a === void 0 ? void 0 : _a.call(on);\n        }, animationDuration);\n    });\n    const handleRef = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node) {\n            handleEnter(node);\n        } else {\n            handleCleanup();\n        }\n    }, [\n        handleEnter,\n        handleCleanup\n    ]);\n    return mounted ? /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxRoot, {\n        ref: handleRef,\n        className: clsx(className, cssClass(cssPrefix$1()), cssClass(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_NO_SCROLL_PADDING), visible && cssClass(cssPrefix$1(\"open\"))),\n        role: \"presentation\",\n        \"aria-live\": \"polite\",\n        style: {\n            ...animation.fade !== LightboxDefaultProps.animation.fade ? {\n                [cssVar(\"fade_animation_duration\")]: `${animationDuration}ms`\n            } : null,\n            ...animation.easing.fade !== LightboxDefaultProps.animation.easing.fade ? {\n                [cssVar(\"fade_animation_timing_function\")]: animation.easing.fade\n            } : null,\n            ...styles.root\n        },\n        onFocus: (event)=>{\n            if (!restoreFocus.current) {\n                restoreFocus.current = event.relatedTarget;\n            }\n        }\n    }, children), portal.root || document.body) : null;\n}\nconst PortalModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_PORTAL, Portal);\nfunction Root({ children }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n}\nconst RootModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_ROOT, Root);\nfunction cssPrefix(value) {\n    return composePrefix(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, value);\n}\nfunction Toolbar({ toolbar: { buttons }, render: { buttonClose, iconClose }, styles }) {\n    const { close, setToolbarWidth } = useController();\n    const { setContainerRef, containerRect } = useContainerRect();\n    useLayoutEffect(()=>{\n        setToolbarWidth(containerRect === null || containerRect === void 0 ? void 0 : containerRect.width);\n    }, [\n        setToolbarWidth,\n        containerRect === null || containerRect === void 0 ? void 0 : containerRect.width\n    ]);\n    const renderCloseButton = ()=>{\n        if (buttonClose) return buttonClose();\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(IconButton, {\n            key: _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE,\n            label: \"Close\",\n            icon: CloseIcon,\n            renderIcon: iconClose,\n            onClick: close\n        });\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        ref: setContainerRef,\n        style: styles.toolbar,\n        className: cssClass(cssPrefix())\n    }, buttons === null || buttons === void 0 ? void 0 : buttons.map((button)=>button === _types_js__WEBPACK_IMPORTED_MODULE_2__.ACTION_CLOSE ? renderCloseButton() : button));\n}\nconst ToolbarModule = createModule(_types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_TOOLBAR, Toolbar);\nfunction renderNode(node, props) {\n    var _a;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(node.module.component, {\n        key: node.module.name,\n        ...props\n    }, (_a = node.children) === null || _a === void 0 ? void 0 : _a.map((child)=>renderNode(child, props)));\n}\nfunction mergeAnimation(defaultAnimation, animation = {}) {\n    const { easing: defaultAnimationEasing, ...restDefaultAnimation } = defaultAnimation;\n    const { easing, ...restAnimation } = animation;\n    return {\n        easing: {\n            ...defaultAnimationEasing,\n            ...easing\n        },\n        ...restDefaultAnimation,\n        ...restAnimation\n    };\n}\nfunction Lightbox({ carousel, animation, render, toolbar, controller, noScroll, on, plugins, slides, index, ...restProps }) {\n    const { animation: defaultAnimation, carousel: defaultCarousel, render: defaultRender, toolbar: defaultToolbar, controller: defaultController, noScroll: defaultNoScroll, on: defaultOn, slides: defaultSlides, index: defaultIndex, plugins: defaultPlugins, ...restDefaultProps } = LightboxDefaultProps;\n    const { config, augmentation } = withPlugins([\n        createNode(PortalModule, [\n            createNode(NoScrollModule, [\n                createNode(ControllerModule, [\n                    createNode(CarouselModule),\n                    createNode(ToolbarModule),\n                    createNode(NavigationModule)\n                ])\n            ])\n        ])\n    ], plugins || defaultPlugins);\n    const props = augmentation({\n        animation: mergeAnimation(defaultAnimation, animation),\n        carousel: {\n            ...defaultCarousel,\n            ...carousel\n        },\n        render: {\n            ...defaultRender,\n            ...render\n        },\n        toolbar: {\n            ...defaultToolbar,\n            ...toolbar\n        },\n        controller: {\n            ...defaultController,\n            ...controller\n        },\n        noScroll: {\n            ...defaultNoScroll,\n            ...noScroll\n        },\n        on: {\n            ...defaultOn,\n            ...on\n        },\n        ...restDefaultProps,\n        ...restProps\n    });\n    if (!props.open) return null;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxPropsProvider, {\n        ...props\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(LightboxStateProvider, {\n        slides: slides || defaultSlides,\n        index: parseInt(index || defaultIndex)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(TimeoutsProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(EventsProvider, null, renderNode(createNode(RootModule, config), props)))));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Captions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst cssPrefix = (className) => (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(`slide_${className}`);\n\nconst defaultCaptionsProps = {\n    descriptionTextAlign: \"start\",\n    descriptionMaxLines: 3,\n    showToggle: false,\n    hidden: false,\n};\nconst resolveCaptionsProps = (captions) => ({\n    ...defaultCaptionsProps,\n    ...captions,\n});\nfunction useCaptionsProps() {\n    const { captions } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    return resolveCaptionsProps(captions);\n}\n\nconst CaptionsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useCaptions = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useCaptions\", \"CaptionsContext\", CaptionsContext);\nfunction CaptionsContextProvider({ captions, children }) {\n    const { ref, hidden } = resolveCaptionsProps(captions);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!hidden);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        visible,\n        show: () => setVisible(true),\n        hide: () => setVisible(false),\n    }), [visible]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(ref, () => context, [context]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(CaptionsContext.Provider, { value: context }, children);\n}\n\nfunction Title({ title }) {\n    const { toolbarWidth } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { styles } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { visible } = useCaptions();\n    if (!visible)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.captionsTitleContainer, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)(cssPrefix(\"captions_container\"), cssPrefix(\"title_container\")) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: cssPrefix(\"title\"), style: {\n                ...(toolbarWidth ? { [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"toolbar_width\")]: `${toolbarWidth}px` } : null),\n                ...styles.captionsTitle,\n            } }, title)));\n}\n\nfunction Description({ description }) {\n    const { descriptionTextAlign, descriptionMaxLines } = useCaptionsProps();\n    const { styles } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { visible } = useCaptions();\n    if (!visible)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.captionsDescriptionContainer, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)(cssPrefix(\"captions_container\"), cssPrefix(\"description_container\")) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: cssPrefix(\"description\"), style: {\n                ...(descriptionTextAlign !== defaultCaptionsProps.descriptionTextAlign ||\n                    descriptionMaxLines !== defaultCaptionsProps.descriptionMaxLines\n                    ? {\n                        [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"slide_description_text_align\")]: descriptionTextAlign,\n                        [(0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssVar)(\"slide_description_max_lines\")]: descriptionMaxLines,\n                    }\n                    : null),\n                ...styles.captionsDescription,\n            } }, typeof description === \"string\"\n            ? description\n                .split(\"\\n\")\n                .flatMap((line, index) => [...(index > 0 ? [react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"br\", { key: index })] : []), line])\n            : description)));\n}\n\nconst captionsIcon = () => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { strokeWidth: 2, stroke: \"currentColor\", strokeLinejoin: \"round\", fill: \"none\", d: \"M3 5l18 0l0 14l-18 0l0-14z\" }),\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M7 15h3c.55 0 1-.45 1-1v-1H9.5v.5h-2v-3h2v.5H11v-1c0-.55-.45-1-1-1H7c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm7 0h3c.55 0 1-.45 1-1v-1h-1.5v.5h-2v-3h2v.5H18v-1c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1z\" })));\nconst CaptionsVisible = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"CaptionsVisible\", captionsIcon());\nconst CaptionsHidden = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIconDisabled)(\"CaptionsVisible\", captionsIcon());\nfunction CaptionsButton() {\n    const { visible, show, hide } = useCaptions();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    if (render.buttonCaptions) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonCaptions({ visible, show, hide }));\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { label: visible ? \"Hide captions\" : \"Show captions\", icon: visible ? CaptionsVisible : CaptionsHidden, renderIcon: visible ? render.iconCaptionsVisible : render.iconCaptionsHidden, onClick: visible ? hide : show }));\n}\n\nfunction Captions({ augment, addModule }) {\n    augment(({ captions: captionsProps, render: { slideFooter: renderFooter, ...restRender }, toolbar, ...restProps }) => {\n        const captions = resolveCaptionsProps(captionsProps);\n        return {\n            render: {\n                slideFooter: ({ slide }) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, renderFooter === null || renderFooter === void 0 ? void 0 :\n                    renderFooter({ slide }),\n                    slide.title && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Title, { title: slide.title }),\n                    slide.description && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Description, { description: slide.description }))),\n                ...restRender,\n            },\n            toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS, captions.showToggle ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(CaptionsButton, null) : null),\n            captions,\n            ...restProps,\n        };\n    });\n    addModule((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_CAPTIONS, CaptionsContextProvider));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/captions/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Download)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultDownloadProps = {\n    download: undefined,\n};\nconst resolveDownloadProps = (download) => ({\n    ...defaultDownloadProps,\n    ...download,\n});\n\nfunction download(url, name) {\n    const xhr = new XMLHttpRequest();\n    xhr.open(\"GET\", url);\n    xhr.responseType = \"blob\";\n    xhr.onload = () => {\n        saveAs(xhr.response, name);\n    };\n    xhr.onerror = () => {\n        console.error(\"Failed to download file\");\n    };\n    xhr.send();\n}\nfunction corsEnabled(url) {\n    const xhr = new XMLHttpRequest();\n    xhr.open(\"HEAD\", url, false);\n    try {\n        xhr.send();\n    }\n    catch (e) {\n    }\n    return xhr.status >= 200 && xhr.status <= 299;\n}\nfunction click(link) {\n    try {\n        link.dispatchEvent(new MouseEvent(\"click\"));\n    }\n    catch (e) {\n        const event = document.createEvent(\"MouseEvents\");\n        event.initMouseEvent(\"click\", true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n        link.dispatchEvent(event);\n    }\n}\nfunction saveAs(source, name) {\n    const link = document.createElement(\"a\");\n    link.rel = \"noopener\";\n    link.download = name || \"\";\n    if (!link.download) {\n        link.target = \"_blank\";\n    }\n    if (typeof source === \"string\") {\n        link.href = source;\n        if (link.origin !== window.location.origin) {\n            if (corsEnabled(link.href)) {\n                download(source, name);\n            }\n            else {\n                link.target = \"_blank\";\n                click(link);\n            }\n        }\n        else {\n            click(link);\n        }\n    }\n    else {\n        link.href = URL.createObjectURL(source);\n        setTimeout(() => URL.revokeObjectURL(link.href), 30000);\n        setTimeout(() => click(link), 0);\n    }\n}\n\nconst DownloadIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"DownloadIcon\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z\" }));\nfunction DownloadButton() {\n    const { render, on, download: downloadProps } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { download: customDownload } = resolveDownloadProps(downloadProps);\n    const { currentSlide, currentIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    if (render.buttonDownload) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonDownload());\n    }\n    const downloadUrl = (currentSlide &&\n        (currentSlide.downloadUrl ||\n            (typeof currentSlide.download === \"string\" && currentSlide.download) ||\n            (typeof currentSlide.download === \"object\" && currentSlide.download.url) ||\n            ((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(currentSlide) && currentSlide.src))) ||\n        undefined;\n    const canDownload = customDownload ? (currentSlide === null || currentSlide === void 0 ? void 0 : currentSlide.download) !== false : Boolean(downloadUrl);\n    const defaultDownload = () => {\n        if (currentSlide && downloadUrl) {\n            const downloadFilename = currentSlide.downloadFilename ||\n                (typeof currentSlide.download === \"object\" && currentSlide.download.filename) ||\n                undefined;\n            saveAs(downloadUrl, downloadFilename);\n        }\n    };\n    const handleDownload = () => {\n        var _a;\n        if (currentSlide) {\n            (customDownload || defaultDownload)({ slide: currentSlide, saveAs });\n            (_a = on.download) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex });\n        }\n    };\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { label: \"Download\", icon: DownloadIcon, renderIcon: render.iconDownload, disabled: !canDownload, onClick: handleDownload }));\n}\n\nfunction Download({ augment }) {\n    augment(({ toolbar, download, ...restProps }) => ({\n        toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_DOWNLOAD, react__WEBPACK_IMPORTED_MODULE_0__.createElement(DownloadButton, null)),\n        download: resolveDownloadProps(download),\n        ...restProps,\n    }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/download/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Fullscreen)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultFullscreenProps = {\n    auto: false,\n    ref: null,\n};\nconst resolveFullscreenProps = (fullscreen) => ({\n    ...defaultFullscreenProps,\n    ...fullscreen,\n});\n\nconst FullscreenContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useFullscreen\", \"FullscreenContext\", FullscreenContext);\nfunction FullscreenContextProvider({ fullscreen: fullscreenProps, on, children }) {\n    const { auto, ref } = resolveFullscreenProps(fullscreenProps);\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [disabled, setDisabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const [fullscreen, setFullscreen] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const wasFullscreen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const { getOwnerDocument } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentContext)();\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        var _a, _b, _c, _d;\n        const ownerDocument = getOwnerDocument();\n        setDisabled(!((_d = (_c = (_b = (_a = ownerDocument.fullscreenEnabled) !== null && _a !== void 0 ? _a : ownerDocument.webkitFullscreenEnabled) !== null && _b !== void 0 ? _b : ownerDocument.mozFullScreenEnabled) !== null && _c !== void 0 ? _c : ownerDocument.msFullscreenEnabled) !== null && _d !== void 0 ? _d : false));\n    }, [getOwnerDocument]);\n    const getFullscreenElement = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        var _a;\n        const ownerDocument = getOwnerDocument();\n        const fullscreenElement = ownerDocument.fullscreenElement ||\n            ownerDocument.webkitFullscreenElement ||\n            ownerDocument.mozFullScreenElement ||\n            ownerDocument.msFullscreenElement;\n        return ((_a = fullscreenElement === null || fullscreenElement === void 0 ? void 0 : fullscreenElement.shadowRoot) === null || _a === void 0 ? void 0 : _a.fullscreenElement) || fullscreenElement;\n    }, [getOwnerDocument]);\n    const enter = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        const container = containerRef.current;\n        try {\n            if (container.requestFullscreen) {\n                container.requestFullscreen().catch(() => { });\n            }\n            else if (container.webkitRequestFullscreen) {\n                container.webkitRequestFullscreen();\n            }\n            else if (container.mozRequestFullScreen) {\n                container.mozRequestFullScreen();\n            }\n            else if (container.msRequestFullscreen) {\n                container.msRequestFullscreen();\n            }\n        }\n        catch (err) {\n        }\n    }, []);\n    const exit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        if (!getFullscreenElement())\n            return;\n        const ownerDocument = getOwnerDocument();\n        try {\n            if (ownerDocument.exitFullscreen) {\n                ownerDocument.exitFullscreen().catch(() => { });\n            }\n            else if (ownerDocument.webkitExitFullscreen) {\n                ownerDocument.webkitExitFullscreen();\n            }\n            else if (ownerDocument.mozCancelFullScreen) {\n                ownerDocument.mozCancelFullScreen();\n            }\n            else if (ownerDocument.msExitFullscreen) {\n                ownerDocument.msExitFullscreen();\n            }\n        }\n        catch (err) {\n        }\n    }, [getFullscreenElement, getOwnerDocument]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const ownerDocument = getOwnerDocument();\n        const listener = () => {\n            setFullscreen(getFullscreenElement() === containerRef.current);\n        };\n        return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cleanup)(...[\"fullscreenchange\", \"webkitfullscreenchange\", \"mozfullscreenchange\", \"MSFullscreenChange\"].map((event) => {\n            ownerDocument.addEventListener(event, listener);\n            return () => ownerDocument.removeEventListener(event, listener);\n        }));\n    }, [getFullscreenElement, getOwnerDocument]);\n    const onEnterFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => { var _a; return (_a = on.enterFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    const onExitFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => { var _a; return (_a = on.exitFullscreen) === null || _a === void 0 ? void 0 : _a.call(on); });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (fullscreen) {\n            wasFullscreen.current = true;\n        }\n        if (wasFullscreen.current) {\n            (fullscreen ? onEnterFullscreen : onExitFullscreen)();\n        }\n    }, [fullscreen, onEnterFullscreen, onExitFullscreen]);\n    const handleAutoFullscreen = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        (_a = (auto ? enter : null)) === null || _a === void 0 ? void 0 : _a();\n        return exit;\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleAutoFullscreen, [handleAutoFullscreen]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ fullscreen, disabled, enter, exit }), [fullscreen, disabled, enter, exit]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(ref, () => context, [context]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: containerRef, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE)) },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullscreenContext.Provider, { value: context }, children)));\n}\n\nconst EnterFullscreenIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"EnterFullscreen\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z\" }));\nconst ExitFullscreenIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ExitFullscreen\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z\" }));\nfunction FullscreenButton() {\n    var _a;\n    const { fullscreen, disabled, enter, exit } = useFullscreen();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    if (disabled)\n        return null;\n    if (render.buttonFullscreen) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (_a = render.buttonFullscreen) === null || _a === void 0 ? void 0 : _a.call(render, { fullscreen, disabled, enter, exit }));\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { disabled: disabled, label: fullscreen ? \"Exit Fullscreen\" : \"Enter Fullscreen\", icon: fullscreen ? ExitFullscreenIcon : EnterFullscreenIcon, renderIcon: fullscreen ? render.iconExitFullscreen : render.iconEnterFullscreen, onClick: fullscreen ? exit : enter }));\n}\n\nfunction Fullscreen({ augment, contains, addParent }) {\n    augment(({ fullscreen, toolbar, ...restProps }) => ({\n        toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN, react__WEBPACK_IMPORTED_MODULE_0__.createElement(FullscreenButton, null)),\n        fullscreen: resolveFullscreenProps(fullscreen),\n        ...restProps,\n    }));\n    addParent(contains(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS) ? _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_THUMBNAILS : _types_js__WEBPACK_IMPORTED_MODULE_2__.MODULE_CONTROLLER, (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_FULLSCREEN, FullscreenContextProvider));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/fullscreen/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zoom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../index.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../types.js */ \"(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\");\n\n\n\n\nconst defaultZoomProps = {\n    maxZoomPixelRatio: 1,\n    zoomInMultiplier: 2,\n    doubleTapDelay: 300,\n    doubleClickDelay: 500,\n    doubleClickMaxStops: 2,\n    keyboardMoveDistance: 50,\n    wheelZoomDistanceFactor: 100,\n    pinchZoomDistanceFactor: 100,\n    scrollToZoom: false,\n};\nconst resolveZoomProps = (zoom) => ({\n    ...defaultZoomProps,\n    ...zoom,\n});\n\nfunction useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef) {\n    const zoomAnimation = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const zoomAnimationStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { zoom: zoomAnimationDuration } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().animation;\n    const reduceMotion = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useMotionPreference)();\n    const playZoomAnimation = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a, _b, _c;\n        (_a = zoomAnimation.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        zoomAnimation.current = undefined;\n        if (zoomAnimationStart.current && (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)) {\n            try {\n                zoomAnimation.current = (_c = (_b = zoomWrapperRef.current).animate) === null || _c === void 0 ? void 0 : _c.call(_b, [\n                    { transform: zoomAnimationStart.current },\n                    { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` },\n                ], {\n                    duration: !reduceMotion ? (zoomAnimationDuration !== null && zoomAnimationDuration !== void 0 ? zoomAnimationDuration : 500) : 0,\n                    easing: zoomAnimation.current ? \"ease-out\" : \"ease-in-out\",\n                });\n            }\n            catch (err) {\n                console.error(err);\n            }\n            zoomAnimationStart.current = undefined;\n            if (zoomAnimation.current) {\n                zoomAnimation.current.onfinish = () => {\n                    zoomAnimation.current = undefined;\n                };\n            }\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(playZoomAnimation, [zoom, offsetX, offsetY, playZoomAnimation]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        zoomAnimationStart.current = (zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current)\n            ? window.getComputedStyle(zoomWrapperRef.current).transform\n            : undefined;\n    }, [zoomWrapperRef]);\n}\n\nfunction useZoomCallback(zoom, disabled) {\n    const { on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const onZoomCallback = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        if (!disabled) {\n            (_a = on.zoom) === null || _a === void 0 ? void 0 : _a.call(on, { zoom });\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(onZoomCallback, [zoom, onZoomCallback]);\n}\n\nfunction useZoomProps() {\n    const { zoom } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    return resolveZoomProps(zoom);\n}\n\nfunction useZoomImageRect(slideRect, imageDimensions) {\n    var _a, _b;\n    let imageRect = { width: 0, height: 0 };\n    let maxImageRect = { width: 0, height: 0 };\n    const { currentSlide } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { imageFit } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)().carousel;\n    const { maxZoomPixelRatio } = useZoomProps();\n    if (slideRect && currentSlide) {\n        const slide = { ...currentSlide, ...imageDimensions };\n        if ((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n            const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(slide, imageFit);\n            const width = Math.max(...(((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.map((x) => x.width)) || []).concat(slide.width ? [slide.width] : []));\n            const height = Math.max(...(((_b = slide.srcSet) === null || _b === void 0 ? void 0 : _b.map((x) => x.height)) || []).concat(slide.height ? [slide.height] : []));\n            if (width > 0 && height > 0 && slideRect.width > 0 && slideRect.height > 0) {\n                maxImageRect = cover\n                    ? {\n                        width: Math.round(Math.min(width, (slideRect.width / slideRect.height) * height)),\n                        height: Math.round(Math.min(height, (slideRect.height / slideRect.width) * width)),\n                    }\n                    : { width, height };\n                maxImageRect = {\n                    width: maxImageRect.width * maxZoomPixelRatio,\n                    height: maxImageRect.height * maxZoomPixelRatio,\n                };\n                imageRect = cover\n                    ? {\n                        width: Math.min(slideRect.width, maxImageRect.width, width),\n                        height: Math.min(slideRect.height, maxImageRect.height, height),\n                    }\n                    : {\n                        width: Math.round(Math.min(slideRect.width, (slideRect.height / height) * width, width)),\n                        height: Math.round(Math.min(slideRect.height, (slideRect.width / width) * height, height)),\n                    };\n            }\n        }\n    }\n    const maxZoom = imageRect.width ? Math.max((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(maxImageRect.width / imageRect.width, 5), 1) : 1;\n    return { imageRect, maxZoom };\n}\n\nfunction distance(pointerA, pointerB) {\n    return ((pointerA.clientX - pointerB.clientX) ** 2 + (pointerA.clientY - pointerB.clientY) ** 2) ** 0.5;\n}\nfunction scaleZoom(value, delta, factor = 100, clamp = 2) {\n    return value * Math.min(1 + Math.abs(delta / factor), clamp) ** Math.sign(delta);\n}\nfunction useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapperRef) {\n    const activePointers = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    const lastPointerDown = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pinchZoomDistance = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const { globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { getOwnerWindow } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentContext)();\n    const { containerRef, subscribeSensors } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { keyboardMoveDistance, zoomInMultiplier, wheelZoomDistanceFactor, scrollToZoom, doubleTapDelay, doubleClickDelay, doubleClickMaxStops, pinchZoomDistanceFactor, } = useZoomProps();\n    const translateCoordinates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        if (containerRef.current) {\n            const { pageX, pageY } = event;\n            const { scrollX, scrollY } = getOwnerWindow();\n            const { left, top, width, height } = containerRef.current.getBoundingClientRect();\n            return [pageX - left - scrollX - width / 2, pageY - top - scrollY - height / 2];\n        }\n        return [];\n    }, [containerRef, getOwnerWindow]);\n    const onKeyDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const preventDefault = () => {\n            event.preventDefault();\n            event.stopPropagation();\n        };\n        if (zoom > 1) {\n            const move = (deltaX, deltaY) => {\n                preventDefault();\n                changeOffsets(deltaX, deltaY);\n            };\n            if (event.key === \"ArrowDown\") {\n                move(0, keyboardMoveDistance);\n            }\n            else if (event.key === \"ArrowUp\") {\n                move(0, -keyboardMoveDistance);\n            }\n            else if (event.key === \"ArrowLeft\") {\n                move(-keyboardMoveDistance, 0);\n            }\n            else if (event.key === \"ArrowRight\") {\n                move(keyboardMoveDistance, 0);\n            }\n        }\n        const handleChangeZoom = (zoomValue) => {\n            preventDefault();\n            changeZoom(zoomValue);\n        };\n        const hasMeta = () => event.getModifierState(\"Meta\");\n        if (event.key === \"+\" || (event.key === \"=\" && hasMeta())) {\n            handleChangeZoom(zoom * zoomInMultiplier);\n        }\n        else if (event.key === \"-\" || (event.key === \"_\" && hasMeta())) {\n            handleChangeZoom(zoom / zoomInMultiplier);\n        }\n        else if (event.key === \"0\" && hasMeta()) {\n            handleChangeZoom(1);\n        }\n    });\n    const onWheel = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        if (event.ctrlKey || scrollToZoom) {\n            if (Math.abs(event.deltaY) > Math.abs(event.deltaX)) {\n                event.stopPropagation();\n                changeZoom(scaleZoom(zoom, -event.deltaY, wheelZoomDistanceFactor), true, ...translateCoordinates(event));\n                return;\n            }\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (!scrollToZoom) {\n                changeOffsets(event.deltaX, event.deltaY);\n            }\n        }\n    });\n    const clearPointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length, ...pointers.filter((p) => p.pointerId !== event.pointerId));\n    }, []);\n    const replacePointer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        clearPointer(event);\n        event.persist();\n        activePointers.current.push(event);\n    }, [clearPointer]);\n    const onPointerDown = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        var _a;\n        const pointers = activePointers.current;\n        if ((event.pointerType === \"mouse\" && event.buttons > 1) ||\n            !((_a = zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current) === null || _a === void 0 ? void 0 : _a.contains(event.target))) {\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n        }\n        const { timeStamp } = event;\n        if (pointers.length === 0 &&\n            timeStamp - lastPointerDown.current < (event.pointerType === \"touch\" ? doubleTapDelay : doubleClickDelay)) {\n            lastPointerDown.current = 0;\n            changeZoom(zoom !== maxZoom ? zoom * Math.max(maxZoom ** (1 / doubleClickMaxStops), zoomInMultiplier) : 1, false, ...translateCoordinates(event));\n        }\n        else {\n            lastPointerDown.current = timeStamp;\n        }\n        replacePointer(event);\n        if (pointers.length === 2) {\n            pinchZoomDistance.current = distance(pointers[0], pointers[1]);\n        }\n    });\n    const onPointerMove = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((event) => {\n        const pointers = activePointers.current;\n        const activePointer = pointers.find((p) => p.pointerId === event.pointerId);\n        if (pointers.length === 2 && pinchZoomDistance.current) {\n            event.stopPropagation();\n            replacePointer(event);\n            const currentDistance = distance(pointers[0], pointers[1]);\n            const delta = currentDistance - pinchZoomDistance.current;\n            if (Math.abs(delta) > 0) {\n                changeZoom(scaleZoom(zoom, delta, pinchZoomDistanceFactor), true, ...pointers\n                    .map((x) => translateCoordinates(x))\n                    .reduce((acc, coordinate) => coordinate.map((x, i) => acc[i] + x / 2)));\n                pinchZoomDistance.current = currentDistance;\n            }\n            return;\n        }\n        if (zoom > 1) {\n            event.stopPropagation();\n            if (activePointer) {\n                if (pointers.length === 1) {\n                    changeOffsets((activePointer.clientX - event.clientX) / zoom, (activePointer.clientY - event.clientY) / zoom);\n                }\n                replacePointer(event);\n            }\n        }\n    });\n    const onPointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event) => {\n        const pointers = activePointers.current;\n        if (pointers.length === 2 && pointers.find((p) => p.pointerId === event.pointerId)) {\n            pinchZoomDistance.current = undefined;\n        }\n        clearPointer(event);\n    }, [clearPointer]);\n    const cleanupSensors = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n        const pointers = activePointers.current;\n        pointers.splice(0, pointers.length);\n        lastPointerDown.current = 0;\n        pinchZoomDistance.current = undefined;\n    }, []);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.usePointerEvents)(subscribeSensors, onPointerDown, onPointerMove, onPointerUp, disabled);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(cleanupSensors, [globalIndex, cleanupSensors]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!disabled) {\n            return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cleanup)(cleanupSensors, subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_KEY_DOWN, onKeyDown), subscribeSensors(_types_js__WEBPACK_IMPORTED_MODULE_2__.EVENT_ON_WHEEL, onWheel));\n        }\n        return () => { };\n    }, [disabled, subscribeSensors, cleanupSensors, onKeyDown, onWheel]);\n}\n\nfunction useZoomState(imageRect, maxZoom, zoomWrapperRef) {\n    const [zoom, setZoom] = react__WEBPACK_IMPORTED_MODULE_0__.useState(1);\n    const [offsetX, setOffsetX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [offsetY, setOffsetY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const animate = useZoomAnimation(zoom, offsetX, offsetY, zoomWrapperRef);\n    const { currentSlide, globalIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    const { containerRect, slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { zoomInMultiplier } = useZoomProps();\n    const currentSource = currentSlide && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(currentSlide) ? currentSlide.src : undefined;\n    const disabled = !currentSource || !(zoomWrapperRef === null || zoomWrapperRef === void 0 ? void 0 : zoomWrapperRef.current);\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        setZoom(1);\n        setOffsetX(0);\n        setOffsetY(0);\n    }, [globalIndex, currentSource]);\n    const changeOffsets = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dx, dy, targetZoom) => {\n        const newZoom = targetZoom || zoom;\n        const newOffsetX = offsetX - (dx || 0);\n        const newOffsetY = offsetY - (dy || 0);\n        const maxOffsetX = (imageRect.width * newZoom - slideRect.width) / 2 / newZoom;\n        const maxOffsetY = (imageRect.height * newZoom - slideRect.height) / 2 / newZoom;\n        setOffsetX(Math.min(Math.abs(newOffsetX), Math.max(maxOffsetX, 0)) * Math.sign(newOffsetX));\n        setOffsetY(Math.min(Math.abs(newOffsetY), Math.max(maxOffsetY, 0)) * Math.sign(newOffsetY));\n    }, [zoom, offsetX, offsetY, slideRect, imageRect.width, imageRect.height]);\n    const changeZoom = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((targetZoom, rapid, dx, dy) => {\n        const newZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.round)(Math.min(Math.max(targetZoom + 0.001 < maxZoom ? targetZoom : maxZoom, 1), maxZoom), 5);\n        if (newZoom === zoom)\n            return;\n        if (!rapid) {\n            animate();\n        }\n        changeOffsets(dx ? dx * (1 / zoom - 1 / newZoom) : 0, dy ? dy * (1 / zoom - 1 / newZoom) : 0, newZoom);\n        setZoom(newZoom);\n    }, [zoom, maxZoom, changeOffsets, animate]);\n    const handleControllerRectChange = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        if (zoom > 1) {\n            if (zoom > maxZoom) {\n                changeZoom(maxZoom, true);\n            }\n            changeOffsets();\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleControllerRectChange, [containerRect.width, containerRect.height, handleControllerRectChange]);\n    const zoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom * zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    const zoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => changeZoom(zoom / zoomInMultiplier), [zoom, zoomInMultiplier, changeZoom]);\n    return { zoom, offsetX, offsetY, disabled, changeOffsets, changeZoom, zoomIn, zoomOut };\n}\n\nconst ZoomControllerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst useZoom = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.makeUseContext)(\"useZoom\", \"ZoomControllerContext\", ZoomControllerContext);\nfunction ZoomContextProvider({ children }) {\n    const [zoomWrapper, setZoomWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const { slideRect } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const { imageRect, maxZoom } = useZoomImageRect(slideRect, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.imageDimensions);\n    const { zoom, offsetX, offsetY, disabled, changeZoom, changeOffsets, zoomIn, zoomOut } = useZoomState(imageRect, maxZoom, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    useZoomCallback(zoom, disabled);\n    useZoomSensors(zoom, maxZoom, disabled, changeZoom, changeOffsets, zoomWrapper === null || zoomWrapper === void 0 ? void 0 : zoomWrapper.zoomWrapperRef);\n    const zoomRef = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom }), [zoom, maxZoom, offsetX, offsetY, disabled, zoomIn, zoomOut, changeZoom]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(useZoomProps().ref, () => zoomRef, [zoomRef]);\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ ...zoomRef, setZoomWrapper }), [zoomRef, setZoomWrapper]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomControllerContext.Provider, { value: context }, children);\n}\n\nconst ZoomInIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomIn\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\" }),\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z\" })));\nconst ZoomOutIcon = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createIcon)(\"ZoomOut\", react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", { d: \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z\" }));\nconst ZoomButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function ZoomButton({ zoomIn, onLoseFocus }, ref) {\n    const wasEnabled = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasFocused = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const { zoom, maxZoom, zoomIn: zoomInCallback, zoomOut: zoomOutCallback, disabled: zoomDisabled } = useZoom();\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const disabled = zoomDisabled || (zoomIn ? zoom >= maxZoom : zoom <= 1);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (disabled && wasEnabled.current && wasFocused.current) {\n            onLoseFocus();\n        }\n        if (!disabled) {\n            wasEnabled.current = true;\n        }\n    }, [disabled, onLoseFocus]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.IconButton, { ref: ref, disabled: disabled, label: zoomIn ? \"Zoom in\" : \"Zoom out\", icon: zoomIn ? ZoomInIcon : ZoomOutIcon, renderIcon: zoomIn ? render.iconZoomIn : render.iconZoomOut, onClick: zoomIn ? zoomInCallback : zoomOutCallback, onFocus: () => {\n            wasFocused.current = true;\n        }, onBlur: () => {\n            wasFocused.current = false;\n        } }));\n});\n\nfunction ZoomButtonsGroup() {\n    const zoomInRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const zoomOutRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { focus } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useController)();\n    const focusSibling = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((sibling) => {\n        var _a, _b;\n        if (!((_a = sibling.current) === null || _a === void 0 ? void 0 : _a.disabled)) {\n            (_b = sibling.current) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n        else {\n            focus();\n        }\n    }, [focus]);\n    const focusZoomIn = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomInRef), [focusSibling]);\n    const focusZoomOut = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => focusSibling(zoomOutRef), [focusSibling]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { zoomIn: true, ref: zoomInRef, onLoseFocus: focusZoomOut }),\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButton, { ref: zoomOutRef, onLoseFocus: focusZoomIn })));\n}\n\nfunction ZoomToolbarControl() {\n    const { render } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const zoomRef = useZoom();\n    if (render.buttonZoom) {\n        return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render.buttonZoom(zoomRef));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomButtonsGroup, null);\n}\n\nfunction isResponsiveImageSlide(slide) {\n    var _a;\n    return (((_a = slide.srcSet) === null || _a === void 0 ? void 0 : _a.length) || 0) > 0;\n}\nfunction reducer({ current, preload }, { type, source }) {\n    switch (type) {\n        case \"fetch\":\n            if (!current) {\n                return { current: source };\n            }\n            return { current, preload: source };\n        case \"done\":\n            if (source === preload) {\n                return { current: source };\n            }\n            return { current, preload };\n        default:\n            throw new Error(_types_js__WEBPACK_IMPORTED_MODULE_2__.UNKNOWN_ACTION_TYPE);\n    }\n}\nfunction ResponsiveImage(props) {\n    var _a, _b;\n    const [{ current, preload }, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(reducer, {});\n    const { slide: image, rect, imageFit, render, interactive } = props;\n    const srcSet = image.srcSet.sort((a, b) => a.width - b.width);\n    const width = (_a = image.width) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1].width;\n    const height = (_b = image.height) !== null && _b !== void 0 ? _b : srcSet[srcSet.length - 1].height;\n    const cover = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageFitCover)(image, imageFit);\n    const maxWidth = Math.max(...srcSet.map((x) => x.width));\n    const targetWidth = Math.min((cover ? Math.max : Math.min)(rect.width, width * (rect.height / height)), maxWidth);\n    const pixelDensity = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.devicePixelRatio)();\n    const handleResize = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)(() => {\n        var _a;\n        const targetSource = (_a = srcSet.find((x) => x.width >= targetWidth * pixelDensity)) !== null && _a !== void 0 ? _a : srcSet[srcSet.length - 1];\n        if (!current || srcSet.findIndex((x) => x.src === current) < srcSet.findIndex((x) => x === targetSource)) {\n            dispatch({ type: \"fetch\", source: targetSource.src });\n        }\n    });\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(handleResize, [rect.width, rect.height, pixelDensity, handleResize]);\n    const handlePreload = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useEventCallback)((currentPreload) => dispatch({ type: \"done\", source: currentPreload }));\n    const style = {\n        WebkitTransform: !interactive ? \"translateZ(0)\" : \"initial\",\n    };\n    if (!cover) {\n        Object.assign(style, rect.width / rect.height < width / height ? { width: \"100%\", height: \"auto\" } : { width: \"auto\", height: \"100%\" });\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n        preload && preload !== current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"preload\", ...props, slide: { ...image, src: preload, srcSet: undefined }, style: { position: \"absolute\", visibility: \"hidden\", ...style }, onLoad: () => handlePreload(preload), render: {\n                ...render,\n                iconLoading: () => null,\n                iconError: () => null,\n            } })),\n        current && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { key: \"current\", ...props, slide: { ...image, src: current, srcSet: undefined }, style: style }))));\n}\n\nfunction ZoomWrapper({ render, slide, offset, rect }) {\n    var _a;\n    const [imageDimensions, setImageDimensions] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const zoomWrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { zoom, maxZoom, offsetX, offsetY, setZoomWrapper } = useZoom();\n    const interactive = zoom > 1;\n    const { carousel, on } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxProps)();\n    const { currentIndex } = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLightboxState)();\n    (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n        if (offset === 0) {\n            setZoomWrapper({ zoomWrapperRef, imageDimensions });\n            return () => setZoomWrapper(undefined);\n        }\n        return () => { };\n    }, [offset, imageDimensions, setZoomWrapper]);\n    let rendered = (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, { slide, offset, rect, zoom, maxZoom });\n    if (!rendered && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(slide)) {\n        const slideProps = {\n            slide,\n            offset,\n            rect,\n            render,\n            imageFit: carousel.imageFit,\n            imageProps: carousel.imageProps,\n            onClick: offset === 0 ? () => { var _a; return (_a = on.click) === null || _a === void 0 ? void 0 : _a.call(on, { index: currentIndex }); } : undefined,\n        };\n        rendered = isResponsiveImageSlide(slide) ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(ResponsiveImage, { ...slideProps, slide: slide, interactive: interactive, rect: offset === 0 ? { width: rect.width * zoom, height: rect.height * zoom } : rect })) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_index_js__WEBPACK_IMPORTED_MODULE_1__.ImageSlide, { onLoad: (img) => setImageDimensions({ width: img.naturalWidth, height: img.naturalHeight }), ...slideProps }));\n    }\n    if (!rendered)\n        return null;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: zoomWrapperRef, className: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.clsx)((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FULLSIZE), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_FLEX_CENTER), (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER), interactive && (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.cssClass)(_types_js__WEBPACK_IMPORTED_MODULE_2__.CLASS_SLIDE_WRAPPER_INTERACTIVE)), style: offset === 0 ? { transform: `scale(${zoom}) translateX(${offsetX}px) translateY(${offsetY}px)` } : undefined }, rendered));\n}\n\nconst Zoom = ({ augment, addModule }) => {\n    augment(({ zoom: zoomProps, toolbar, render, controller, ...restProps }) => {\n        const zoom = resolveZoomProps(zoomProps);\n        return {\n            zoom,\n            toolbar: (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.addToolbarButton)(toolbar, _types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomToolbarControl, null)),\n            render: {\n                ...render,\n                slide: (props) => { var _a; return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.isImageSlide)(props.slide) ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(ZoomWrapper, { render: render, ...props }) : (_a = render.slide) === null || _a === void 0 ? void 0 : _a.call(render, props); },\n            },\n            controller: { ...controller, preventDefaultWheelY: zoom.scrollToZoom },\n            ...restProps,\n        };\n    });\n    addModule((0,_index_js__WEBPACK_IMPORTED_MODULE_1__.createModule)(_types_js__WEBPACK_IMPORTED_MODULE_2__.PLUGIN_ZOOM, ZoomContextProvider));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/plugins/zoom/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_CLOSE: () => (/* binding */ ACTION_CLOSE),\n/* harmony export */   ACTION_NEXT: () => (/* binding */ ACTION_NEXT),\n/* harmony export */   ACTION_PREV: () => (/* binding */ ACTION_PREV),\n/* harmony export */   ACTION_SWIPE: () => (/* binding */ ACTION_SWIPE),\n/* harmony export */   ACTIVE_SLIDE_COMPLETE: () => (/* binding */ ACTIVE_SLIDE_COMPLETE),\n/* harmony export */   ACTIVE_SLIDE_ERROR: () => (/* binding */ ACTIVE_SLIDE_ERROR),\n/* harmony export */   ACTIVE_SLIDE_LOADING: () => (/* binding */ ACTIVE_SLIDE_LOADING),\n/* harmony export */   ACTIVE_SLIDE_PLAYING: () => (/* binding */ ACTIVE_SLIDE_PLAYING),\n/* harmony export */   CLASS_FLEX_CENTER: () => (/* binding */ CLASS_FLEX_CENTER),\n/* harmony export */   CLASS_FULLSIZE: () => (/* binding */ CLASS_FULLSIZE),\n/* harmony export */   CLASS_NO_SCROLL: () => (/* binding */ CLASS_NO_SCROLL),\n/* harmony export */   CLASS_NO_SCROLL_PADDING: () => (/* binding */ CLASS_NO_SCROLL_PADDING),\n/* harmony export */   CLASS_SLIDE_WRAPPER: () => (/* binding */ CLASS_SLIDE_WRAPPER),\n/* harmony export */   CLASS_SLIDE_WRAPPER_INTERACTIVE: () => (/* binding */ CLASS_SLIDE_WRAPPER_INTERACTIVE),\n/* harmony export */   ELEMENT_BUTTON: () => (/* binding */ ELEMENT_BUTTON),\n/* harmony export */   ELEMENT_ICON: () => (/* binding */ ELEMENT_ICON),\n/* harmony export */   EVENT_ON_KEY_DOWN: () => (/* binding */ EVENT_ON_KEY_DOWN),\n/* harmony export */   EVENT_ON_KEY_UP: () => (/* binding */ EVENT_ON_KEY_UP),\n/* harmony export */   EVENT_ON_POINTER_CANCEL: () => (/* binding */ EVENT_ON_POINTER_CANCEL),\n/* harmony export */   EVENT_ON_POINTER_DOWN: () => (/* binding */ EVENT_ON_POINTER_DOWN),\n/* harmony export */   EVENT_ON_POINTER_LEAVE: () => (/* binding */ EVENT_ON_POINTER_LEAVE),\n/* harmony export */   EVENT_ON_POINTER_MOVE: () => (/* binding */ EVENT_ON_POINTER_MOVE),\n/* harmony export */   EVENT_ON_POINTER_UP: () => (/* binding */ EVENT_ON_POINTER_UP),\n/* harmony export */   EVENT_ON_WHEEL: () => (/* binding */ EVENT_ON_WHEEL),\n/* harmony export */   IMAGE_FIT_CONTAIN: () => (/* binding */ IMAGE_FIT_CONTAIN),\n/* harmony export */   IMAGE_FIT_COVER: () => (/* binding */ IMAGE_FIT_COVER),\n/* harmony export */   MODULE_CAROUSEL: () => (/* binding */ MODULE_CAROUSEL),\n/* harmony export */   MODULE_CONTROLLER: () => (/* binding */ MODULE_CONTROLLER),\n/* harmony export */   MODULE_NAVIGATION: () => (/* binding */ MODULE_NAVIGATION),\n/* harmony export */   MODULE_NO_SCROLL: () => (/* binding */ MODULE_NO_SCROLL),\n/* harmony export */   MODULE_PORTAL: () => (/* binding */ MODULE_PORTAL),\n/* harmony export */   MODULE_ROOT: () => (/* binding */ MODULE_ROOT),\n/* harmony export */   MODULE_TOOLBAR: () => (/* binding */ MODULE_TOOLBAR),\n/* harmony export */   PLUGIN_CAPTIONS: () => (/* binding */ PLUGIN_CAPTIONS),\n/* harmony export */   PLUGIN_COUNTER: () => (/* binding */ PLUGIN_COUNTER),\n/* harmony export */   PLUGIN_DOWNLOAD: () => (/* binding */ PLUGIN_DOWNLOAD),\n/* harmony export */   PLUGIN_FULLSCREEN: () => (/* binding */ PLUGIN_FULLSCREEN),\n/* harmony export */   PLUGIN_INLINE: () => (/* binding */ PLUGIN_INLINE),\n/* harmony export */   PLUGIN_SHARE: () => (/* binding */ PLUGIN_SHARE),\n/* harmony export */   PLUGIN_SLIDESHOW: () => (/* binding */ PLUGIN_SLIDESHOW),\n/* harmony export */   PLUGIN_THUMBNAILS: () => (/* binding */ PLUGIN_THUMBNAILS),\n/* harmony export */   PLUGIN_ZOOM: () => (/* binding */ PLUGIN_ZOOM),\n/* harmony export */   SLIDE_STATUS_COMPLETE: () => (/* binding */ SLIDE_STATUS_COMPLETE),\n/* harmony export */   SLIDE_STATUS_ERROR: () => (/* binding */ SLIDE_STATUS_ERROR),\n/* harmony export */   SLIDE_STATUS_LOADING: () => (/* binding */ SLIDE_STATUS_LOADING),\n/* harmony export */   SLIDE_STATUS_PLACEHOLDER: () => (/* binding */ SLIDE_STATUS_PLACEHOLDER),\n/* harmony export */   SLIDE_STATUS_PLAYING: () => (/* binding */ SLIDE_STATUS_PLAYING),\n/* harmony export */   UNKNOWN_ACTION_TYPE: () => (/* binding */ UNKNOWN_ACTION_TYPE),\n/* harmony export */   VK_ARROW_LEFT: () => (/* binding */ VK_ARROW_LEFT),\n/* harmony export */   VK_ARROW_RIGHT: () => (/* binding */ VK_ARROW_RIGHT),\n/* harmony export */   VK_ESCAPE: () => (/* binding */ VK_ESCAPE),\n/* harmony export */   activeSlideStatus: () => (/* binding */ activeSlideStatus)\n/* harmony export */ });\nconst MODULE_CAROUSEL = \"carousel\";\nconst MODULE_CONTROLLER = \"controller\";\nconst MODULE_NAVIGATION = \"navigation\";\nconst MODULE_NO_SCROLL = \"no-scroll\";\nconst MODULE_PORTAL = \"portal\";\nconst MODULE_ROOT = \"root\";\nconst MODULE_TOOLBAR = \"toolbar\";\nconst PLUGIN_CAPTIONS = \"captions\";\nconst PLUGIN_COUNTER = \"counter\";\nconst PLUGIN_DOWNLOAD = \"download\";\nconst PLUGIN_FULLSCREEN = \"fullscreen\";\nconst PLUGIN_INLINE = \"inline\";\nconst PLUGIN_SHARE = \"share\";\nconst PLUGIN_SLIDESHOW = \"slideshow\";\nconst PLUGIN_THUMBNAILS = \"thumbnails\";\nconst PLUGIN_ZOOM = \"zoom\";\nconst SLIDE_STATUS_LOADING = \"loading\";\nconst SLIDE_STATUS_PLAYING = \"playing\";\nconst SLIDE_STATUS_ERROR = \"error\";\nconst SLIDE_STATUS_COMPLETE = \"complete\";\nconst SLIDE_STATUS_PLACEHOLDER = \"placeholder\";\nconst activeSlideStatus = (status) => `active-slide-${status}`;\nconst ACTIVE_SLIDE_LOADING = activeSlideStatus(SLIDE_STATUS_LOADING);\nconst ACTIVE_SLIDE_PLAYING = activeSlideStatus(SLIDE_STATUS_PLAYING);\nconst ACTIVE_SLIDE_ERROR = activeSlideStatus(SLIDE_STATUS_ERROR);\nconst ACTIVE_SLIDE_COMPLETE = activeSlideStatus(SLIDE_STATUS_COMPLETE);\nconst CLASS_FULLSIZE = \"fullsize\";\nconst CLASS_FLEX_CENTER = \"flex_center\";\nconst CLASS_NO_SCROLL = \"no_scroll\";\nconst CLASS_NO_SCROLL_PADDING = \"no_scroll_padding\";\nconst CLASS_SLIDE_WRAPPER = \"slide_wrapper\";\nconst CLASS_SLIDE_WRAPPER_INTERACTIVE = \"slide_wrapper_interactive\";\nconst ACTION_PREV = \"prev\";\nconst ACTION_NEXT = \"next\";\nconst ACTION_SWIPE = \"swipe\";\nconst ACTION_CLOSE = \"close\";\nconst EVENT_ON_POINTER_DOWN = \"onPointerDown\";\nconst EVENT_ON_POINTER_MOVE = \"onPointerMove\";\nconst EVENT_ON_POINTER_UP = \"onPointerUp\";\nconst EVENT_ON_POINTER_LEAVE = \"onPointerLeave\";\nconst EVENT_ON_POINTER_CANCEL = \"onPointerCancel\";\nconst EVENT_ON_KEY_DOWN = \"onKeyDown\";\nconst EVENT_ON_KEY_UP = \"onKeyUp\";\nconst EVENT_ON_WHEEL = \"onWheel\";\nconst VK_ESCAPE = \"Escape\";\nconst VK_ARROW_LEFT = \"ArrowLeft\";\nconst VK_ARROW_RIGHT = \"ArrowRight\";\nconst ELEMENT_BUTTON = \"button\";\nconst ELEMENT_ICON = \"icon\";\nconst IMAGE_FIT_CONTAIN = \"contain\";\nconst IMAGE_FIT_COVER = \"cover\";\nconst UNKNOWN_ACTION_TYPE = \"Unknown action type\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/yet-another-react-lightbox@_25a9284067fe3a8ece91f48f2a488c38/node_modules/yet-another-react-lightbox/dist/types.js\n");

/***/ })

};
;