"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.1_95283094e99ff394dd713b5a46c9c816";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.1_95283094e99ff394dd713b5a46c9c816"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_95283094e99ff394dd713b5a46c9c816/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_95283094e99ff394dd713b5a46c9c816/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_fe5e3e67b6d7a8cbcdac508a3412d002/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._f09ca44b075894bef1f22048f2189343/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._f6427c6d64d2d542f3fe67e5d488b7cd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_df97a09d62161aad15439954749cb418/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_96e5d54b7bd10cf53cadcc132d77f938/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // packages/react/tooltip/src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const skipDelayTimer = skipDelayTimerRef.current;\n        return ()=>window.clearTimeout(skipDelayTimer);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayed,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            setIsOpenDelayed(false);\n        }, []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            skipDelayTimerRef.current = window.setTimeout(()=>setIsOpenDelayed(true), skipDelayDuration);\n        }, [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((inTransit)=>{\n            isPointerInTransitRef.current = inTransit;\n        }, []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen = false, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: (open2)=>{\n            if (open2) {\n                providerContext.onOpen();\n                document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n            } else {\n                providerContext.onClose();\n            }\n            onOpenChange?.(open2);\n        }\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n    }, [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        wasOpenDelayedRef.current = false;\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = window.setTimeout(()=>{\n            wasOpenDelayedRef.current = true;\n            setOpen(true);\n            openTimerRef.current = 0;\n        }, delayDuration);\n    }, [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (openTimerRef.current) {\n                window.clearTimeout(openTimerRef.current);\n                openTimerRef.current = 0;\n            }\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (providerContext.isOpenDelayed) handleDelayedOpen();\n                else handleOpen();\n            }, [\n                providerContext.isOpenDelayed,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (disableHoverableContent) {\n                    handleClose();\n                } else {\n                    window.clearTimeout(openTimerRef.current);\n                    openTimerRef.current = 0;\n                }\n            }, [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>isPointerDownRef.current = false, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        setPointerGraceArea(null);\n        onPointerInTransitChange(false);\n    }, [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event, hoverTarget)=>{\n        const currentTarget = event.currentTarget;\n        const exitPoint = {\n            x: event.clientX,\n            y: event.clientY\n        };\n        const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n        const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n        const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n        const graceArea = getHull([\n            ...paddedExitPoints,\n            ...hoverTargetPoints\n        ]);\n        setPointerGraceArea(graceArea);\n        onPointerInTransitChange(true);\n    }, [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>handleRemoveGraceArea();\n    }, [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trigger && content) {\n            const handleTriggerLeave = (event)=>handleCreateGraceArea(event, content);\n            const handleContentLeave = (event)=>handleCreateGraceArea(event, trigger);\n            trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n            content.addEventListener(\"pointerleave\", handleContentLeave);\n            return ()=>{\n                trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                content.removeEventListener(\"pointerleave\", handleContentLeave);\n            };\n        }\n    }, [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (pointerGraceArea) {\n            const handleTrackPointerGrace = (event)=>{\n                const target = event.target;\n                const pointerPosition = {\n                    x: event.clientX,\n                    y: event.clientY\n                };\n                const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                if (hasEnteredTarget) {\n                    handleRemoveGraceArea();\n                } else if (isPointerOutsideGraceArea) {\n                    handleRemoveGraceArea();\n                    onClose();\n                }\n            };\n            document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n            return ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n        }\n    }, [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        document.addEventListener(TOOLTIP_OPEN, onClose);\n        return ()=>document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (context.trigger) {\n            const handleScroll = (event)=>{\n                const target = event.target;\n                if (target?.contains(context.trigger)) onClose();\n            };\n            window.addEventListener(\"scroll\", handleScroll, {\n                capture: true\n            });\n            return ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n        }\n    }, [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_95283094e99ff394dd713b5a46c9c816/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;