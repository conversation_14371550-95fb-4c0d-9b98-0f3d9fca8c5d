"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@userback+widget@0.3.7";
exports.ids = ["vendor-chunks/@userback+widget@0.3.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@userback+widget@0.3.7/node_modules/@userback/widget/dist/widget.mjs":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@userback+widget@0.3.7/node_modules/@userback/widget/dist/widget.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserbackWidgetLoader),\n/* harmony export */   getUserback: () => (/* binding */ getUserback)\n/* harmony export */ });\n// An internal reference of the `window.Userback` object which will\n// be deleted from the `window` scope when using this module.\nlet USERBACK;\n/* eslint-enable no-unused-vars */\n// internal variable for storing a pending load of the widget to prevent loading the widget twice\n// When undefined, the widget is not currently loading.\nlet UBLoadingPromise;\n/*\n * UserbackWidgetLoader\n *\n * Provides a type-safe interface for initializing and retrieving the Userback object\n * @param token - The Userback token to use for initialisation\n * @param ubOptions - Optional configuration options for the Userback widget\n * @returns A promise that resolves to the UserbackWidget object\n */\nfunction UserbackWidgetLoader(token, ubOptions) {\n    if (UBLoadingPromise)\n        return UBLoadingPromise;\n    UBLoadingPromise = new Promise((resolve, reject) => {\n        // Validation\n        const error = (e) => {\n            UBLoadingPromise = undefined;\n            return reject(typeof e === 'string' ? new Error(e) : e);\n        };\n        if (typeof USERBACK !== 'undefined') {\n            // eslint-disable-next-line no-console\n            console.debug('Userback widget loaded twice, canceling initialisation');\n            return resolve(USERBACK);\n        }\n        if (!token) {\n            return error('A valid token must be provided from https://userback.io');\n        }\n        // Defaults\n        const opts = typeof ubOptions === 'undefined' ? {} : ubOptions;\n        const ubDomain = (opts === null || opts === void 0 ? void 0 : opts.domain) || 'userback.io';\n        // Custom options\n        window.Userback = { request_url: `https://api.${ubDomain}` };\n        if (opts === null || opts === void 0 ? void 0 : opts.autohide) {\n            if (!opts.widget_settings) {\n                opts.widget_settings = {};\n            }\n            opts.widget_settings.trigger_type = opts.autohide ? 'api' : 'page_load';\n        }\n        // When the script tag is finished loading, we will move the `window.Userback` reference to\n        // this local module and then provide it back as a promise resolution.\n        function onload() {\n            if (typeof window.Userback === 'undefined') {\n                return error('`window.Userback` was somehow deleted while loading!');\n            }\n            window.Userback.init(token, Object.assign(Object.assign({}, opts), { on_init: () => {\n                    USERBACK = window.Userback;\n                    // @TODO: Cannot remove window.Userback as there are references inside the widget to it\n                    // delete window.Userback\n                    if (typeof (opts === null || opts === void 0 ? void 0 : opts.on_init) === 'function') {\n                        opts.on_init();\n                    }\n                    // Monkeypatch Userback.destroy to ensure we keep our USERBACK reference in sync\n                    const origDestroy = USERBACK.destroy;\n                    USERBACK.destroy = function proxyDestroy() {\n                        origDestroy();\n                        USERBACK = undefined;\n                        UBLoadingPromise = undefined;\n                    };\n                    return resolve(USERBACK);\n                } }));\n            return true;\n        }\n        // Create and inject the <script/> tag to start loading Userback\n        const script = document.createElement('script');\n        script.src = `https://static.${ubDomain}/widget/v1.js`;\n        script.async = true;\n        script.onload = onload;\n        script.addEventListener('error', error);\n        document.body.appendChild(script);\n        return true;\n    });\n    return UBLoadingPromise;\n}\n/**\n * Returns the UserbackWidget if it has been initialised\n * */\nfunction getUserback() {\n    return USERBACK;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@userback+widget@0.3.7/node_modules/@userback/widget/dist/widget.mjs\n");

/***/ })

};
;