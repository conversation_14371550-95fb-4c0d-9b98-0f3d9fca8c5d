"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1._4619683047f5c087c37ca832062ba5ad/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.6.13/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.2_6451095dc1f245ec0bdc7b8b1b7b3cde/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_4171a71166cfa012d93ab3a234657034/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_89c4c6402d5b83b03c66d469fb85e060/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._1b26e4441db58cb57140f1b7eab022f4/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;