"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1._ed81356e6adb04107a9c0929a995826c";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1._ed81356e6adb04107a9c0929a995826c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._ed81356e6adb04107a9c0929a995826c/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-avatar@1.1._ed81356e6adb04107a9c0929a995826c/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_4171a71166cfa012d93ab3a234657034/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_89c4c6402d5b83b03c66d469fb85e060/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // packages/react/avatar/src/avatar.tsx\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)((status)=>{\n        onLoadingStatusChange(status);\n        context.onImageLoadingStatusChange(status);\n    });\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (imageLoadingStatus !== \"idle\") {\n            handleLoadingStatusChange(imageLoadingStatus);\n        }\n    }, [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (delayMs !== void 0) {\n            const timerId = window.setTimeout(()=>setCanRender(true), delayMs);\n            return ()=>window.clearTimeout(timerId);\n        }\n    }, [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction useImageLoadingStatus(src, referrerPolicy) {\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (!src) {\n            setLoadingStatus(\"error\");\n            return;\n        }\n        let isMounted = true;\n        const image = new window.Image();\n        const updateStatus = (status)=>()=>{\n                if (!isMounted) return;\n                setLoadingStatus(status);\n            };\n        setLoadingStatus(\"loading\");\n        image.onload = updateStatus(\"loaded\");\n        image.onerror = updateStatus(\"error\");\n        image.src = src;\n        if (referrerPolicy) {\n            image.referrerPolicy = referrerPolicy;\n        }\n        return ()=>{\n            isMounted = false;\n        };\n    }, [\n        src,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._ed81356e6adb04107a9c0929a995826c/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;