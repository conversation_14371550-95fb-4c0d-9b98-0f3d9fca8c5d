"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry+caches@1.0.1";
exports.ids = ["vendor-chunks/@wry+caches@1.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StrongCache: () => (/* binding */ StrongCache)\n/* harmony export */ });\nfunction defaultDispose() { }\nclass StrongCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new Map();\n        this.newest = null;\n        this.oldest = null;\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    get size() {\n        return this.map.size;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return node.value = value;\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.map.set(key, node);\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.map.size > this.max) {\n            this.delete(this.oldest.key);\n        }\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            if (node === this.newest) {\n                this.newest = node.older;\n            }\n            if (node === this.oldest) {\n                this.oldest = node.newer;\n            }\n            if (node.newer) {\n                node.newer.older = node.older;\n            }\n            if (node.older) {\n                node.older.newer = node.newer;\n            }\n            this.map.delete(key);\n            this.dispose(node.value, key);\n            return true;\n        }\n        return false;\n    }\n}\n//# sourceMappingURL=strong.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/weak.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/weak.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeakCache: () => (/* binding */ WeakCache)\n/* harmony export */ });\nfunction noop() { }\nconst defaultDispose = noop;\nconst _WeakRef = typeof WeakRef !== \"undefined\"\n    ? WeakRef\n    : function (value) {\n        return { deref: () => value };\n    };\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry = typeof FinalizationRegistry !== \"undefined\"\n    ? FinalizationRegistry\n    : function () {\n        return {\n            register: noop,\n            unregister: noop,\n        };\n    };\nconst finalizationBatchSize = 10024;\nclass WeakCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new _WeakMap();\n        this.newest = null;\n        this.oldest = null;\n        this.unfinalizedNodes = new Set();\n        this.finalizationScheduled = false;\n        this.size = 0;\n        this.finalize = () => {\n            const iterator = this.unfinalizedNodes.values();\n            for (let i = 0; i < finalizationBatchSize; i++) {\n                const node = iterator.next().value;\n                if (!node)\n                    break;\n                this.unfinalizedNodes.delete(node);\n                const key = node.key;\n                delete node.key;\n                node.keyRef = new _WeakRef(key);\n                this.registry.register(key, node, node);\n            }\n            if (this.unfinalizedNodes.size > 0) {\n                queueMicrotask(this.finalize);\n            }\n            else {\n                this.finalizationScheduled = false;\n            }\n        };\n        this.registry = new _FinalizationRegistry(this.deleteNode.bind(this));\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return (node.value = value);\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest,\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.scheduleFinalization(node);\n        this.map.set(key, node);\n        this.size++;\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.size > this.max) {\n            this.deleteNode(this.oldest);\n        }\n    }\n    deleteNode(node) {\n        if (node === this.newest) {\n            this.newest = node.older;\n        }\n        if (node === this.oldest) {\n            this.oldest = node.newer;\n        }\n        if (node.newer) {\n            node.newer.older = node.older;\n        }\n        if (node.older) {\n            node.older.newer = node.newer;\n        }\n        this.size--;\n        const key = node.key || (node.keyRef && node.keyRef.deref());\n        this.dispose(node.value, key);\n        if (!node.keyRef) {\n            this.unfinalizedNodes.delete(node);\n        }\n        else {\n            this.registry.unregister(node);\n        }\n        if (key)\n            this.map.delete(key);\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            this.deleteNode(node);\n            return true;\n        }\n        return false;\n    }\n    scheduleFinalization(node) {\n        this.unfinalizedNodes.add(node);\n        if (!this.finalizationScheduled) {\n            this.finalizationScheduled = true;\n            queueMicrotask(this.finalize);\n        }\n    }\n}\n//# sourceMappingURL=weak.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/weak.js\n");

/***/ })

};
;