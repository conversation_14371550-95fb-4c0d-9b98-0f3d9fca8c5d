/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlbuilder@11.0.1";
exports.ids = ["vendor-chunks/xmlbuilder@11.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/DocumentPosition.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/DocumentPosition.js ***!
  \**********************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Disconnected: 1,\n    Preceding: 2,\n    Following: 4,\n    Contains: 8,\n    ContainedBy: 16,\n    ImplementationSpecific: 32\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL0RvY3VtZW50UG9zaXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL0RvY3VtZW50UG9zaXRpb24uanM/MTc1MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICBtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBEaXNjb25uZWN0ZWQ6IDEsXG4gICAgUHJlY2VkaW5nOiAyLFxuICAgIEZvbGxvd2luZzogNCxcbiAgICBDb250YWluczogOCxcbiAgICBDb250YWluZWRCeTogMTYsXG4gICAgSW1wbGVtZW50YXRpb25TcGVjaWZpYzogMzJcbiAgfTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/DocumentPosition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js ***!
  \**************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL05vZGVUeXBlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvTm9kZVR5cGUuanM/ZDFiNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICBtb2R1bGUuZXhwb3J0cyA9IHtcbiAgICBFbGVtZW50OiAxLFxuICAgIEF0dHJpYnV0ZTogMixcbiAgICBUZXh0OiAzLFxuICAgIENEYXRhOiA0LFxuICAgIEVudGl0eVJlZmVyZW5jZTogNSxcbiAgICBFbnRpdHlEZWNsYXJhdGlvbjogNixcbiAgICBQcm9jZXNzaW5nSW5zdHJ1Y3Rpb246IDcsXG4gICAgQ29tbWVudDogOCxcbiAgICBEb2N1bWVudDogOSxcbiAgICBEb2NUeXBlOiAxMCxcbiAgICBEb2N1bWVudEZyYWdtZW50OiAxMSxcbiAgICBOb3RhdGlvbkRlY2xhcmF0aW9uOiAxMixcbiAgICBEZWNsYXJhdGlvbjogMjAxLFxuICAgIFJhdzogMjAyLFxuICAgIEF0dHJpYnV0ZURlY2xhcmF0aW9uOiAyMDMsXG4gICAgRWxlbWVudERlY2xhcmF0aW9uOiAyMDQsXG4gICAgRHVtbXk6IDIwNVxuICB9O1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js ***!
  \*************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js ***!
  \*****************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1dyaXRlclN0YXRlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1dyaXRlclN0YXRlLmpzPzIyODMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgTm9uZTogMCxcbiAgICBPcGVuVGFnOiAxLFxuICAgIEluc2lkZVRhZzogMixcbiAgICBDbG9zZVRhZzogM1xuICB9O1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLAttribute.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLAttribute.js ***!
  \******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLNode;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.value = this.stringify.attValue(value);\n      this.type = NodeType.Attribute;\n      this.isId = false;\n      this.schemaTypeInfo = null;\n    }\n\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function() {\n        return true;\n      }\n    });\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLAttribute.prototype.isEqualNode = function(node) {\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.value !== this.value) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLAttribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js ***!
  \**************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCData, XMLCharacterData,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLCData;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js ***!
  \**********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCharacterData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLCharacterData = (function(superClass) {\n    extend(XMLCharacterData, superClass);\n\n    function XMLCharacterData(parent) {\n      XMLCharacterData.__super__.constructor.call(this, parent);\n      this.value = '';\n    }\n\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function() {\n        return this.value.length;\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    XMLCharacterData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCharacterData.prototype.substringData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.appendData = function(arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.insertData = function(offset, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.deleteData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.replaceData = function(offset, count, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.isEqualNode = function(node) {\n      if (!XMLCharacterData.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.data !== this.data) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLCharacterData;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js ***!
  \****************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLComment,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLComment;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMConfiguration.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMConfiguration.js ***!
  \*************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMConfiguration, XMLDOMErrorHandler, XMLDOMStringList;\n\n  XMLDOMErrorHandler = __webpack_require__(/*! ./XMLDOMErrorHandler */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\");\n\n  XMLDOMStringList = __webpack_require__(/*! ./XMLDOMStringList */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMStringList.js\");\n\n  module.exports = XMLDOMConfiguration = (function() {\n    function XMLDOMConfiguration() {\n      var clonedSelf;\n      this.defaultParams = {\n        \"canonical-form\": false,\n        \"cdata-sections\": false,\n        \"comments\": false,\n        \"datatype-normalization\": false,\n        \"element-content-whitespace\": true,\n        \"entities\": true,\n        \"error-handler\": new XMLDOMErrorHandler(),\n        \"infoset\": true,\n        \"validate-if-schema\": false,\n        \"namespaces\": true,\n        \"namespace-declarations\": true,\n        \"normalize-characters\": false,\n        \"schema-location\": '',\n        \"schema-type\": '',\n        \"split-cdata-sections\": true,\n        \"validate\": false,\n        \"well-formed\": true\n      };\n      this.params = clonedSelf = Object.create(this.defaultParams);\n    }\n\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function() {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n\n    XMLDOMConfiguration.prototype.getParameter = function(name) {\n      if (this.params.hasOwnProperty(name)) {\n        return this.params[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLDOMConfiguration.prototype.canSetParameter = function(name, value) {\n      return true;\n    };\n\n    XMLDOMConfiguration.prototype.setParameter = function(name, value) {\n      if (value != null) {\n        return this.params[name] = value;\n      } else {\n        return delete this.params[name];\n      }\n    };\n\n    return XMLDOMConfiguration;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTUNvbmZpZ3VyYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBOztBQUVBLHVCQUF1QixtQkFBTyxDQUFDLDRIQUFzQjs7QUFFckQscUJBQXFCLG1CQUFPLENBQUMsd0hBQW9COztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NQ29uZmlndXJhdGlvbi5qcz9iZWRhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMS4xMi43XG4oZnVuY3Rpb24oKSB7XG4gIHZhciBYTUxET01Db25maWd1cmF0aW9uLCBYTUxET01FcnJvckhhbmRsZXIsIFhNTERPTVN0cmluZ0xpc3Q7XG5cbiAgWE1MRE9NRXJyb3JIYW5kbGVyID0gcmVxdWlyZSgnLi9YTUxET01FcnJvckhhbmRsZXInKTtcblxuICBYTUxET01TdHJpbmdMaXN0ID0gcmVxdWlyZSgnLi9YTUxET01TdHJpbmdMaXN0Jyk7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxET01Db25maWd1cmF0aW9uID0gKGZ1bmN0aW9uKCkge1xuICAgIGZ1bmN0aW9uIFhNTERPTUNvbmZpZ3VyYXRpb24oKSB7XG4gICAgICB2YXIgY2xvbmVkU2VsZjtcbiAgICAgIHRoaXMuZGVmYXVsdFBhcmFtcyA9IHtcbiAgICAgICAgXCJjYW5vbmljYWwtZm9ybVwiOiBmYWxzZSxcbiAgICAgICAgXCJjZGF0YS1zZWN0aW9uc1wiOiBmYWxzZSxcbiAgICAgICAgXCJjb21tZW50c1wiOiBmYWxzZSxcbiAgICAgICAgXCJkYXRhdHlwZS1ub3JtYWxpemF0aW9uXCI6IGZhbHNlLFxuICAgICAgICBcImVsZW1lbnQtY29udGVudC13aGl0ZXNwYWNlXCI6IHRydWUsXG4gICAgICAgIFwiZW50aXRpZXNcIjogdHJ1ZSxcbiAgICAgICAgXCJlcnJvci1oYW5kbGVyXCI6IG5ldyBYTUxET01FcnJvckhhbmRsZXIoKSxcbiAgICAgICAgXCJpbmZvc2V0XCI6IHRydWUsXG4gICAgICAgIFwidmFsaWRhdGUtaWYtc2NoZW1hXCI6IGZhbHNlLFxuICAgICAgICBcIm5hbWVzcGFjZXNcIjogdHJ1ZSxcbiAgICAgICAgXCJuYW1lc3BhY2UtZGVjbGFyYXRpb25zXCI6IHRydWUsXG4gICAgICAgIFwibm9ybWFsaXplLWNoYXJhY3RlcnNcIjogZmFsc2UsXG4gICAgICAgIFwic2NoZW1hLWxvY2F0aW9uXCI6ICcnLFxuICAgICAgICBcInNjaGVtYS10eXBlXCI6ICcnLFxuICAgICAgICBcInNwbGl0LWNkYXRhLXNlY3Rpb25zXCI6IHRydWUsXG4gICAgICAgIFwidmFsaWRhdGVcIjogZmFsc2UsXG4gICAgICAgIFwid2VsbC1mb3JtZWRcIjogdHJ1ZVxuICAgICAgfTtcbiAgICAgIHRoaXMucGFyYW1zID0gY2xvbmVkU2VsZiA9IE9iamVjdC5jcmVhdGUodGhpcy5kZWZhdWx0UGFyYW1zKTtcbiAgICB9XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MRE9NQ29uZmlndXJhdGlvbi5wcm90b3R5cGUsICdwYXJhbWV0ZXJOYW1lcycsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBuZXcgWE1MRE9NU3RyaW5nTGlzdChPYmplY3Qua2V5cyh0aGlzLmRlZmF1bHRQYXJhbXMpKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIFhNTERPTUNvbmZpZ3VyYXRpb24ucHJvdG90eXBlLmdldFBhcmFtZXRlciA9IGZ1bmN0aW9uKG5hbWUpIHtcbiAgICAgIGlmICh0aGlzLnBhcmFtcy5oYXNPd25Qcm9wZXJ0eShuYW1lKSkge1xuICAgICAgICByZXR1cm4gdGhpcy5wYXJhbXNbbmFtZV07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgWE1MRE9NQ29uZmlndXJhdGlvbi5wcm90b3R5cGUuY2FuU2V0UGFyYW1ldGVyID0gZnVuY3Rpb24obmFtZSwgdmFsdWUpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH07XG5cbiAgICBYTUxET01Db25maWd1cmF0aW9uLnByb3RvdHlwZS5zZXRQYXJhbWV0ZXIgPSBmdW5jdGlvbihuYW1lLCB2YWx1ZSkge1xuICAgICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucGFyYW1zW25hbWVdID0gdmFsdWU7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gZGVsZXRlIHRoaXMucGFyYW1zW25hbWVdO1xuICAgICAgfVxuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRE9NQ29uZmlndXJhdGlvbjtcblxuICB9KSgpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js ***!
  \************************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMErrorHandler;\n\n  module.exports = XMLDOMErrorHandler = (function() {\n    function XMLDOMErrorHandler() {}\n\n    XMLDOMErrorHandler.prototype.handleError = function(error) {\n      throw new Error(error);\n    };\n\n    return XMLDOMErrorHandler;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTUVycm9ySGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NRXJyb3JIYW5kbGVyLmpzPzUwYmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTERPTUVycm9ySGFuZGxlcjtcblxuICBtb2R1bGUuZXhwb3J0cyA9IFhNTERPTUVycm9ySGFuZGxlciA9IChmdW5jdGlvbigpIHtcbiAgICBmdW5jdGlvbiBYTUxET01FcnJvckhhbmRsZXIoKSB7fVxuXG4gICAgWE1MRE9NRXJyb3JIYW5kbGVyLnByb3RvdHlwZS5oYW5kbGVFcnJvciA9IGZ1bmN0aW9uKGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IpO1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRE9NRXJyb3JIYW5kbGVyO1xuXG4gIH0pKCk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMImplementation.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMImplementation.js ***!
  \**************************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMImplementation;\n\n  module.exports = XMLDOMImplementation = (function() {\n    function XMLDOMImplementation() {}\n\n    XMLDOMImplementation.prototype.hasFeature = function(feature, version) {\n      return true;\n    };\n\n    XMLDOMImplementation.prototype.createDocumentType = function(qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createDocument = function(namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createHTMLDocument = function(title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLDOMImplementation;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTUltcGxlbWVudGF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NSW1wbGVtZW50YXRpb24uanM/YThhMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgWE1MRE9NSW1wbGVtZW50YXRpb247XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxET01JbXBsZW1lbnRhdGlvbiA9IChmdW5jdGlvbigpIHtcbiAgICBmdW5jdGlvbiBYTUxET01JbXBsZW1lbnRhdGlvbigpIHt9XG5cbiAgICBYTUxET01JbXBsZW1lbnRhdGlvbi5wcm90b3R5cGUuaGFzRmVhdHVyZSA9IGZ1bmN0aW9uKGZlYXR1cmUsIHZlcnNpb24pIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH07XG5cbiAgICBYTUxET01JbXBsZW1lbnRhdGlvbi5wcm90b3R5cGUuY3JlYXRlRG9jdW1lbnRUeXBlID0gZnVuY3Rpb24ocXVhbGlmaWVkTmFtZSwgcHVibGljSWQsIHN5c3RlbUlkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiKTtcbiAgICB9O1xuXG4gICAgWE1MRE9NSW1wbGVtZW50YXRpb24ucHJvdG90eXBlLmNyZWF0ZURvY3VtZW50ID0gZnVuY3Rpb24obmFtZXNwYWNlVVJJLCBxdWFsaWZpZWROYW1lLCBkb2N0eXBlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiKTtcbiAgICB9O1xuXG4gICAgWE1MRE9NSW1wbGVtZW50YXRpb24ucHJvdG90eXBlLmNyZWF0ZUhUTUxEb2N1bWVudCA9IGZ1bmN0aW9uKHRpdGxlKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiKTtcbiAgICB9O1xuXG4gICAgWE1MRE9NSW1wbGVtZW50YXRpb24ucHJvdG90eXBlLmdldEZlYXR1cmUgPSBmdW5jdGlvbihmZWF0dXJlLCB2ZXJzaW9uKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGlzIERPTSBtZXRob2QgaXMgbm90IGltcGxlbWVudGVkLlwiKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTERPTUltcGxlbWVudGF0aW9uO1xuXG4gIH0pKCk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMImplementation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMStringList.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMStringList.js ***!
  \**********************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMStringList;\n\n  module.exports = XMLDOMStringList = (function() {\n    function XMLDOMStringList(arr) {\n      this.arr = arr || [];\n    }\n\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function() {\n        return this.arr.length;\n      }\n    });\n\n    XMLDOMStringList.prototype.item = function(index) {\n      return this.arr[index] || null;\n    };\n\n    XMLDOMStringList.prototype.contains = function(str) {\n      return this.arr.indexOf(str) !== -1;\n    };\n\n    return XMLDOMStringList;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTVN0cmluZ0xpc3QuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLEdBQUc7O0FBRUgsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTVN0cmluZ0xpc3QuanM/YWY1YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgWE1MRE9NU3RyaW5nTGlzdDtcblxuICBtb2R1bGUuZXhwb3J0cyA9IFhNTERPTVN0cmluZ0xpc3QgPSAoZnVuY3Rpb24oKSB7XG4gICAgZnVuY3Rpb24gWE1MRE9NU3RyaW5nTGlzdChhcnIpIHtcbiAgICAgIHRoaXMuYXJyID0gYXJyIHx8IFtdO1xuICAgIH1cblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShYTUxET01TdHJpbmdMaXN0LnByb3RvdHlwZSwgJ2xlbmd0aCcsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmFyci5sZW5ndGg7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBYTUxET01TdHJpbmdMaXN0LnByb3RvdHlwZS5pdGVtID0gZnVuY3Rpb24oaW5kZXgpIHtcbiAgICAgIHJldHVybiB0aGlzLmFycltpbmRleF0gfHwgbnVsbDtcbiAgICB9O1xuXG4gICAgWE1MRE9NU3RyaW5nTGlzdC5wcm90b3R5cGUuY29udGFpbnMgPSBmdW5jdGlvbihzdHIpIHtcbiAgICAgIHJldHVybiB0aGlzLmFyci5pbmRleE9mKHN0cikgIT09IC0xO1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRE9NU3RyaW5nTGlzdDtcblxuICB9KSgpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMStringList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js ***!
  \*******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js ***!
  \*******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js ***!
  \******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.name(name);\n      this.type = NodeType.EntityDeclaration;\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n        this.internal = true;\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        this.internal = false;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function() {\n        return this.nData || null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js ***!
  \********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.NotationDeclaration;\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js ***!
  \********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js ***!
  \****************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNamedNodeMap, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var child, i, len, ref, ref1, ref2;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.type = NodeType.DocType;\n      if (parent.children) {\n        ref = parent.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.Element) {\n            this.name = child.name;\n            break;\n          }\n        }\n      }\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref1 = pubID, pubID = ref1.pubID, sysID = ref1.sysID;\n      }\n      if (sysID == null) {\n        ref2 = [pubID, sysID], sysID = ref2[0], pubID = ref2[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDocType.prototype, 'entities', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if ((child.type === NodeType.EntityDeclaration) && !child.pe) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'notations', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.NotationDeclaration) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'internalSubset', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.docType(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    XMLDocType.prototype.isEqualNode = function(node) {\n      if (!XMLDocType.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.name !== this.name) {\n        return false;\n      }\n      if (node.publicId !== this.publicId) {\n        return false;\n      }\n      if (node.systemId !== this.systemId) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocument.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocument.js ***!
  \*****************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDOMConfiguration, XMLDOMImplementation, XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\").isPlainObject);\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDOMConfiguration = __webpack_require__(/*! ./XMLDOMConfiguration */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"#document\";\n      this.type = NodeType.Document;\n      this.documentURI = null;\n      this.domConfig = new XMLDOMConfiguration();\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n    }\n\n    Object.defineProperty(XMLDocument.prototype, 'implementation', {\n      value: new XMLDOMImplementation()\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'doctype', {\n      get: function() {\n        var child, i, len, ref;\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.DocType) {\n            return child;\n          }\n        }\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'documentElement', {\n      get: function() {\n        return this.rootObject || null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'strictErrorChecking', {\n      get: function() {\n        return false;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlEncoding', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].encoding;\n        } else {\n          return null;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlStandalone', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].standalone === 'yes';\n        } else {\n          return false;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlVersion', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].version;\n        } else {\n          return \"1.0\";\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'URL', {\n      get: function() {\n        return this.documentURI;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'origin', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'compatMode', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'characterSet', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'contentType', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      writerOptions = {};\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer;\n      }\n      return writer.document(this, writer.filterOptions(writerOptions));\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.document(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocument.prototype.createElement = function(tagName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createDocumentFragment = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTextNode = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createComment = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createCDATASection = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createProcessingInstruction = function(target, data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttribute = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEntityReference = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.importNode = function(importedNode, deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createElementNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttributeNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementById = function(elementId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.adoptNode = function(source) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.normalizeDocument = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.renameNode = function(node, namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEvent = function(eventInterface) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createRange = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createNodeIterator = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTreeWalker = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocument.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocumentCB.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocumentCB.js ***!
  \*******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocument, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.createChildNode = function(node) {\n      var att, attName, attributes, child, i, len, ref1, ref2;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref1 = node.attribs;\n          for (attName in ref1) {\n            if (!hasProp.call(ref1, attName)) continue;\n            att = ref1[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref2 = node.children;\n      for (i = 0, len = ref2.length; i < len; i++) {\n        child = ref2[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dummy = function() {\n      return this;\n    };\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref1, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement.apply(this, arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref1 = root.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      var att, chunk, name, ref1;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref1 = node.attribs;\n          for (name in ref1) {\n            if (!hasProp.call(ref1, name)) continue;\n            att = ref1[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag;\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocumentCB.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDummy.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDummy.js ***!
  \**************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.type = NodeType.Dummy;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTER1bW15LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QywwQkFBMEIsMkRBQTJELGtCQUFrQiw0QkFBNEIsbUNBQW1DLDhCQUE4QixvQ0FBb0MsZUFBZTtBQUM5UixnQkFBZ0I7O0FBRWhCLFlBQVksbUJBQU8sQ0FBQyxzR0FBVzs7QUFFL0IsYUFBYSxtQkFBTyxDQUFDLHdHQUFZOztBQUVqQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRHVtbXkuanM/OWI2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgTm9kZVR5cGUsIFhNTER1bW15LCBYTUxOb2RlLFxuICAgIGV4dGVuZCA9IGZ1bmN0aW9uKGNoaWxkLCBwYXJlbnQpIHsgZm9yICh2YXIga2V5IGluIHBhcmVudCkgeyBpZiAoaGFzUHJvcC5jYWxsKHBhcmVudCwga2V5KSkgY2hpbGRba2V5XSA9IHBhcmVudFtrZXldOyB9IGZ1bmN0aW9uIGN0b3IoKSB7IHRoaXMuY29uc3RydWN0b3IgPSBjaGlsZDsgfSBjdG9yLnByb3RvdHlwZSA9IHBhcmVudC5wcm90b3R5cGU7IGNoaWxkLnByb3RvdHlwZSA9IG5ldyBjdG9yKCk7IGNoaWxkLl9fc3VwZXJfXyA9IHBhcmVudC5wcm90b3R5cGU7IHJldHVybiBjaGlsZDsgfSxcbiAgICBoYXNQcm9wID0ge30uaGFzT3duUHJvcGVydHk7XG5cbiAgWE1MTm9kZSA9IHJlcXVpcmUoJy4vWE1MTm9kZScpO1xuXG4gIE5vZGVUeXBlID0gcmVxdWlyZSgnLi9Ob2RlVHlwZScpO1xuXG4gIG1vZHVsZS5leHBvcnRzID0gWE1MRHVtbXkgPSAoZnVuY3Rpb24oc3VwZXJDbGFzcykge1xuICAgIGV4dGVuZChYTUxEdW1teSwgc3VwZXJDbGFzcyk7XG5cbiAgICBmdW5jdGlvbiBYTUxEdW1teShwYXJlbnQpIHtcbiAgICAgIFhNTER1bW15Ll9fc3VwZXJfXy5jb25zdHJ1Y3Rvci5jYWxsKHRoaXMsIHBhcmVudCk7XG4gICAgICB0aGlzLnR5cGUgPSBOb2RlVHlwZS5EdW1teTtcbiAgICB9XG5cbiAgICBYTUxEdW1teS5wcm90b3R5cGUuY2xvbmUgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiBPYmplY3QuY3JlYXRlKHRoaXMpO1xuICAgIH07XG5cbiAgICBYTUxEdW1teS5wcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbihvcHRpb25zKSB7XG4gICAgICByZXR1cm4gJyc7XG4gICAgfTtcblxuICAgIHJldHVybiBYTUxEdW1teTtcblxuICB9KShYTUxOb2RlKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDummy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js ***!
  \****************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLElement, XMLNamedNodeMap, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      var child, j, len, ref1;\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.Element;\n      this.attribs = {};\n      this.schemaTypeInfo = null;\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.type === NodeType.Document) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n        if (parent.children) {\n          ref1 = parent.children;\n          for (j = 0, len = ref1.length; j < len; j++) {\n            child = ref1[j];\n            if (child.type === NodeType.DocType) {\n              child.name = this.name;\n              break;\n            }\n          }\n        }\n      }\n    }\n\n    Object.defineProperty(XMLElement.prototype, 'tagName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'id', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'className', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'classList', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'attributes', {\n      get: function() {\n        if (!this.attributeMap || !this.attributeMap.nodes) {\n          this.attributeMap = new XMLNamedNodeMap(this.attribs);\n        }\n        return this.attributeMap;\n      }\n    });\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attribs = {};\n      ref1 = this.attribs;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attribs[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, j, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          attName = name[j];\n          delete this.attribs[attName];\n        }\n      } else {\n        delete this.attribs[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.element(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.getAttribute = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].value;\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttribute = function(name, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNode = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttributeNode = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNode = function(oldAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNS = function(namespaceURI, qualifiedName, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNodeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNodeNS = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.hasAttribute = function(name) {\n      return this.attribs.hasOwnProperty(name);\n    };\n\n    XMLElement.prototype.hasAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttribute = function(name, isId) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].isId;\n      } else {\n        return isId;\n      }\n    };\n\n    XMLElement.prototype.setIdAttributeNS = function(namespaceURI, localName, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttributeNode = function(idAttr, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.isEqualNode = function(node) {\n      var i, j, ref1;\n      if (!XMLElement.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.attribs.length !== this.attribs.length) {\n        return false;\n      }\n      for (i = j = 0, ref1 = this.attribs.length - 1; 0 <= ref1 ? j <= ref1 : j >= ref1; i = 0 <= ref1 ? ++j : --j) {\n        if (!this.attribs[i].isEqualNode(node.attribs[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js ***!
  \*********************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNamedNodeMap;\n\n  module.exports = XMLNamedNodeMap = (function() {\n    function XMLNamedNodeMap(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function() {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n\n    XMLNamedNodeMap.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItem = function(name) {\n      return this.nodes[name];\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItem = function(node) {\n      var oldNode;\n      oldNode = this.nodes[node.nodeName];\n      this.nodes[node.nodeName] = node;\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItem = function(name) {\n      var oldNode;\n      oldNode = this.nodes[name];\n      delete this.nodes[name];\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.item = function(index) {\n      return this.nodes[Object.keys(this.nodes)[index]] || null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItemNS = function(node) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLNamedNodeMap;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js ***!
  \*************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var DocumentPosition, NodeType, XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNamedNodeMap, XMLNode, XMLNodeList, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref1,\n    hasProp = {}.hasOwnProperty;\n\n  ref1 = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref1.isObject, isFunction = ref1.isFunction, isEmpty = ref1.isEmpty, getValue = ref1.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  NodeType = null;\n\n  XMLNodeList = null;\n\n  XMLNamedNodeMap = null;\n\n  DocumentPosition = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent1) {\n      this.parent = parent1;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.value = null;\n      this.children = [];\n      this.baseURI = null;\n      if (!XMLElement) {\n        XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js\");\n        XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js\");\n        XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js\");\n        XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n        XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js\");\n        XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js\");\n        XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js\");\n        XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n        XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDummy.js\");\n        NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n        XMLNodeList = __webpack_require__(/*! ./XMLNodeList */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNodeList.js\");\n        XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n        DocumentPosition = __webpack_require__(/*! ./DocumentPosition */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/DocumentPosition.js\");\n      }\n    }\n\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function() {\n        return this.value;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function() {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function() {\n        return this.children[0] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function() {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function() {\n        return this.document() || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function() {\n        var child, j, len, ref2, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref2 = this.children;\n          for (j = 0, len = ref2.length; j < len; j++) {\n            child = ref2[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function(value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLNode.prototype.setParent = function(parent) {\n      var child, j, len, ref2, results;\n      this.parent = parent;\n      if (parent) {\n        this.options = parent.options;\n        this.stringify = parent.stringify;\n      }\n      ref2 = this.children;\n      results = [];\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        results.push(child.setParent(this));\n      }\n      return results;\n    };\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref2, ref3, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref2 = [{}, null], attributes = ref2[0], text = ref2[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref3 = [attributes, text], text = ref3[0], attributes = ref3[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n            lastChild = this.dummy();\n          } else if (isObject(val) && isEmpty(val)) {\n            lastChild = this.element(key);\n          } else if (!this.options.keepNullNodes && (val == null)) {\n            lastChild = this.dummy();\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n              lastChild = this.element(val);\n            } else {\n              lastChild = this.element(key);\n              lastChild.element(val);\n            }\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (!this.options.keepNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, newChild, refChild, removed;\n      if (name != null ? name.type : void 0) {\n        newChild = name;\n        refChild = attributes;\n        newChild.setParent(this);\n        if (refChild) {\n          i = children.indexOf(refChild);\n          removed = children.splice(i);\n          children.push(newChild);\n          Array.prototype.push.apply(children, removed);\n        } else {\n          children.push(newChild);\n        }\n        return newChild;\n      } else {\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        child = this.parent.element(name, attributes, text);\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref2;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref2 = [])), ref2;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref2;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      if (isObject(value)) {\n        this.element(value);\n      }\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children.length === 0) {\n        doc.children.unshift(xmldec);\n      } else if (doc.children[0].type === NodeType.Declaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref2, ref3;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref2 = doc.children;\n      for (i = j = 0, len = ref2.length; j < len; i = ++j) {\n        child = ref2[i];\n        if (child.type === NodeType.DocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref3 = doc.children;\n      for (i = k = 0, len1 = ref3.length; k < len1; i = ++k) {\n        child = ref3[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref2, ref3;\n      name = name || this.name;\n      if ((name == null) && !((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref3 = this.parent) != null ? ref3.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    XMLNode.prototype.replaceChild = function(newChild, oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.removeChild = function(oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.appendChild = function(newChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.hasChildNodes = function() {\n      return this.children.length !== 0;\n    };\n\n    XMLNode.prototype.cloneNode = function(deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.normalize = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isSupported = function(feature, version) {\n      return true;\n    };\n\n    XMLNode.prototype.hasAttributes = function() {\n      return this.attribs.length !== 0;\n    };\n\n    XMLNode.prototype.compareDocumentPosition = function(other) {\n      var ref, res;\n      ref = this;\n      if (ref === other) {\n        return 0;\n      } else if (this.document() !== other.document()) {\n        res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n        if (Math.random() < 0.5) {\n          res |= DocumentPosition.Preceding;\n        } else {\n          res |= DocumentPosition.Following;\n        }\n        return res;\n      } else if (ref.isAncestor(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Preceding;\n      } else if (ref.isDescendant(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Following;\n      } else if (ref.isPreceding(other)) {\n        return DocumentPosition.Preceding;\n      } else {\n        return DocumentPosition.Following;\n      }\n    };\n\n    XMLNode.prototype.isSameNode = function(other) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupPrefix = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isDefaultNamespace = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupNamespaceURI = function(prefix) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isEqualNode = function(node) {\n      var i, j, ref2;\n      if (node.nodeType !== this.nodeType) {\n        return false;\n      }\n      if (node.children.length !== this.children.length) {\n        return false;\n      }\n      for (i = j = 0, ref2 = this.children.length - 1; 0 <= ref2 ? j <= ref2 : j >= ref2; i = 0 <= ref2 ? ++j : --j) {\n        if (!this.children[i].isEqualNode(node.children[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    XMLNode.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.setUserData = function(key, data, handler) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.getUserData = function(key) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.contains = function(other) {\n      if (!other) {\n        return false;\n      }\n      return other === this || this.isDescendant(other);\n    };\n\n    XMLNode.prototype.isDescendant = function(node) {\n      var child, isDescendantChild, j, len, ref2;\n      ref2 = this.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (node === child) {\n          return true;\n        }\n        isDescendantChild = child.isDescendant(node);\n        if (isDescendantChild) {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    XMLNode.prototype.isAncestor = function(node) {\n      return node.isDescendant(this);\n    };\n\n    XMLNode.prototype.isPreceding = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos < thisPos;\n      }\n    };\n\n    XMLNode.prototype.isFollowing = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos > thisPos;\n      }\n    };\n\n    XMLNode.prototype.treePosition = function(node) {\n      var found, pos;\n      pos = 0;\n      found = false;\n      this.foreachTreeNode(this.document(), function(childNode) {\n        pos++;\n        if (!found && childNode === node) {\n          return found = true;\n        }\n      });\n      if (found) {\n        return pos;\n      } else {\n        return -1;\n      }\n    };\n\n    XMLNode.prototype.foreachTreeNode = function(node, func) {\n      var child, j, len, ref2, res;\n      node || (node = this.document());\n      ref2 = node.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (res = func(child)) {\n          return res;\n        } else {\n          res = this.foreachTreeNode(child, func);\n          if (res) {\n            return res;\n          }\n        }\n      }\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNodeList.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNodeList.js ***!
  \*****************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNodeList;\n\n  module.exports = XMLNodeList = (function() {\n    function XMLNodeList(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function() {\n        return this.nodes.length || 0;\n      }\n    });\n\n    XMLNodeList.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNodeList.prototype.item = function(index) {\n      return this.nodes[index] || null;\n    };\n\n    return XMLNodeList;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTE5vZGVMaXN0LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3htbGJ1aWxkZXJAMTEuMC4xL25vZGVfbW9kdWxlcy94bWxidWlsZGVyL2xpYi9YTUxOb2RlTGlzdC5qcz8zZmJmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMS4xMi43XG4oZnVuY3Rpb24oKSB7XG4gIHZhciBYTUxOb2RlTGlzdDtcblxuICBtb2R1bGUuZXhwb3J0cyA9IFhNTE5vZGVMaXN0ID0gKGZ1bmN0aW9uKCkge1xuICAgIGZ1bmN0aW9uIFhNTE5vZGVMaXN0KG5vZGVzKSB7XG4gICAgICB0aGlzLm5vZGVzID0gbm9kZXM7XG4gICAgfVxuXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KFhNTE5vZGVMaXN0LnByb3RvdHlwZSwgJ2xlbmd0aCcsIHtcbiAgICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm5vZGVzLmxlbmd0aCB8fCAwO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgWE1MTm9kZUxpc3QucHJvdG90eXBlLmNsb25lID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gdGhpcy5ub2RlcyA9IG51bGw7XG4gICAgfTtcblxuICAgIFhNTE5vZGVMaXN0LnByb3RvdHlwZS5pdGVtID0gZnVuY3Rpb24oaW5kZXgpIHtcbiAgICAgIHJldHVybiB0aGlzLm5vZGVzW2luZGV4XSB8fCBudWxsO1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MTm9kZUxpc3Q7XG5cbiAgfSkoKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNodeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js ***!
  \******************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLProcessingInstruction.prototype.isEqualNode = function(node) {\n      if (!XMLProcessingInstruction.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0veG1sYnVpbGRlckAxMS4wLjEvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTFByb2Nlc3NpbmdJbnN0cnVjdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsMEJBQTBCLDJEQUEyRCxrQkFBa0IsNEJBQTRCLG1DQUFtQyw4QkFBOEIsb0NBQW9DLGVBQWU7QUFDOVIsZ0JBQWdCOztBQUVoQixhQUFhLG1CQUFPLENBQUMsd0dBQVk7O0FBRWpDLHFCQUFxQixtQkFBTyxDQUFDLHdIQUFvQjs7QUFFakQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS94bWxidWlsZGVyQDExLjAuMS9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uLmpzPzliMmIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIE5vZGVUeXBlLCBYTUxDaGFyYWN0ZXJEYXRhLCBYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24sXG4gICAgZXh0ZW5kID0gZnVuY3Rpb24oY2hpbGQsIHBhcmVudCkgeyBmb3IgKHZhciBrZXkgaW4gcGFyZW50KSB7IGlmIChoYXNQcm9wLmNhbGwocGFyZW50LCBrZXkpKSBjaGlsZFtrZXldID0gcGFyZW50W2tleV07IH0gZnVuY3Rpb24gY3RvcigpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGNoaWxkOyB9IGN0b3IucHJvdG90eXBlID0gcGFyZW50LnByb3RvdHlwZTsgY2hpbGQucHJvdG90eXBlID0gbmV3IGN0b3IoKTsgY2hpbGQuX19zdXBlcl9fID0gcGFyZW50LnByb3RvdHlwZTsgcmV0dXJuIGNoaWxkOyB9LFxuICAgIGhhc1Byb3AgPSB7fS5oYXNPd25Qcm9wZXJ0eTtcblxuICBOb2RlVHlwZSA9IHJlcXVpcmUoJy4vTm9kZVR5cGUnKTtcblxuICBYTUxDaGFyYWN0ZXJEYXRhID0gcmVxdWlyZSgnLi9YTUxDaGFyYWN0ZXJEYXRhJyk7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24gPSAoZnVuY3Rpb24oc3VwZXJDbGFzcykge1xuICAgIGV4dGVuZChYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24sIHN1cGVyQ2xhc3MpO1xuXG4gICAgZnVuY3Rpb24gWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uKHBhcmVudCwgdGFyZ2V0LCB2YWx1ZSkge1xuICAgICAgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uLl9fc3VwZXJfXy5jb25zdHJ1Y3Rvci5jYWxsKHRoaXMsIHBhcmVudCk7XG4gICAgICBpZiAodGFyZ2V0ID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTWlzc2luZyBpbnN0cnVjdGlvbiB0YXJnZXQuIFwiICsgdGhpcy5kZWJ1Z0luZm8oKSk7XG4gICAgICB9XG4gICAgICB0aGlzLnR5cGUgPSBOb2RlVHlwZS5Qcm9jZXNzaW5nSW5zdHJ1Y3Rpb247XG4gICAgICB0aGlzLnRhcmdldCA9IHRoaXMuc3RyaW5naWZ5Lmluc1RhcmdldCh0YXJnZXQpO1xuICAgICAgdGhpcy5uYW1lID0gdGhpcy50YXJnZXQ7XG4gICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgdGhpcy52YWx1ZSA9IHRoaXMuc3RyaW5naWZ5Lmluc1ZhbHVlKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24ucHJvdG90eXBlLmNsb25lID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gT2JqZWN0LmNyZWF0ZSh0aGlzKTtcbiAgICB9O1xuXG4gICAgWE1MUHJvY2Vzc2luZ0luc3RydWN0aW9uLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uKG9wdGlvbnMpIHtcbiAgICAgIHJldHVybiB0aGlzLm9wdGlvbnMud3JpdGVyLnByb2Nlc3NpbmdJbnN0cnVjdGlvbih0aGlzLCB0aGlzLm9wdGlvbnMud3JpdGVyLmZpbHRlck9wdGlvbnMob3B0aW9ucykpO1xuICAgIH07XG5cbiAgICBYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24ucHJvdG90eXBlLmlzRXF1YWxOb2RlID0gZnVuY3Rpb24obm9kZSkge1xuICAgICAgaWYgKCFYTUxQcm9jZXNzaW5nSW5zdHJ1Y3Rpb24uX19zdXBlcl9fLmlzRXF1YWxOb2RlLmFwcGx5KHRoaXMsIGFyZ3VtZW50cykuaXNFcXVhbE5vZGUobm9kZSkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgICAgaWYgKG5vZGUudGFyZ2V0ICE9PSB0aGlzLnRhcmdldCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTFByb2Nlc3NpbmdJbnN0cnVjdGlvbjtcblxuICB9KShYTUxDaGFyYWN0ZXJEYXRhKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js ***!
  \************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStreamWriter.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStreamWriter.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLStreamWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      this.stream = stream;\n      XMLStreamWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStreamWriter.prototype.endline = function(node, options, level) {\n      if (node.isLastRootNode && options.state === WriterState.CloseTag) {\n        return '';\n      } else {\n        return XMLStreamWriter.__super__.endline.call(this, node, options, level);\n      }\n    };\n\n    XMLStreamWriter.prototype.document = function(doc, options) {\n      var child, i, j, k, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = j = 0, len = ref.length; j < len; i = ++j) {\n        child = ref[i];\n        child.isLastRootNode = i === doc.children.length - 1;\n      }\n      options = this.filterOptions(options);\n      ref1 = doc.children;\n      results = [];\n      for (k = 0, len1 = ref1.length; k < len1; k++) {\n        child = ref1[k];\n        results.push(this.writeChildNode(child, options, 0));\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.attribute.call(this, att, options, level));\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.cdata.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.comment.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.declaration.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, options, level) {\n      var child, j, len, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (j = 0, len = ref.length; j < len; j++) {\n          child = ref[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(']');\n      }\n      options.state = WriterState.CloseTag;\n      this.stream.write(options.spaceBeforeSlash + '>');\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, j, len, name, prettySuppressed, ref, ref1;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level) + '<' + node.name);\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          this.stream.write('>');\n          options.state = WriterState.CloseTag;\n          this.stream.write('</' + node.name + '>');\n        } else {\n          options.state = WriterState.CloseTag;\n          this.stream.write(options.spaceBeforeSlash + '/>');\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        this.stream.write('>');\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref1 = node.children;\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(this.indent(node, options, level) + '</' + node.name + '>');\n      }\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.processingInstruction.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.raw.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.text.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdAttList.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdElement.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdEntity.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdNotation.call(this, node, options, level));\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStreamWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringifier.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringifier.js ***!
  \********************************************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalName = bind(this.assertLegalName, this);\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      if (!this.options.version) {\n        this.options.version = '1.0';\n      }\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.name = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalName('' + val || '');\n    };\n\n    XMLStringifier.prototype.text = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.textEscape('' + val || ''));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.attEscape(val = '' + val || ''));\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var regex, res;\n      if (this.options.noValidation) {\n        return str;\n      }\n      regex = '';\n      if (this.options.version === '1.0') {\n        regex = /[\\0-\\x08\\x0B\\f\\x0E-\\x1F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      } else if (this.options.version === '1.1') {\n        regex = /[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.assertLegalName = function(str) {\n      var regex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      this.assertLegalChar(str);\n      regex = /^([:A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])([\\x2D\\.0-:A-Z_a-z\\xB7\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u203F\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])*$/;\n      if (!str.match(regex)) {\n        throw new Error(\"Invalid character in name\");\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.textEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js ***!
  \*************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.name = \"#text\";\n      this.type = NodeType.Text;\n      this.value = this.stringify.text(text);\n    }\n\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function() {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.text(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLText.prototype.splitText = function(offset) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLText.prototype.replaceWholeText = function(content) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLText;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLWriterBase.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLWriterBase.js ***!
  \*******************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLText, XMLWriterBase, assign,\n    hasProp = {}.hasOwnProperty;\n\n  assign = (__webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\").assign);\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[\"_\" + key] = this[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.filterOptions = function(options) {\n      var filteredOptions, ref, ref1, ref2, ref3, ref4, ref5, ref6;\n      options || (options = {});\n      options = assign({}, this.options, options);\n      filteredOptions = {\n        writer: this\n      };\n      filteredOptions.pretty = options.pretty || false;\n      filteredOptions.allowEmpty = options.allowEmpty || false;\n      filteredOptions.indent = (ref = options.indent) != null ? ref : '  ';\n      filteredOptions.newline = (ref1 = options.newline) != null ? ref1 : '\\n';\n      filteredOptions.offset = (ref2 = options.offset) != null ? ref2 : 0;\n      filteredOptions.dontPrettyTextNodes = (ref3 = (ref4 = options.dontPrettyTextNodes) != null ? ref4 : options.dontprettytextnodes) != null ? ref3 : 0;\n      filteredOptions.spaceBeforeSlash = (ref5 = (ref6 = options.spaceBeforeSlash) != null ? ref6 : options.spacebeforeslash) != null ? ref5 : '';\n      if (filteredOptions.spaceBeforeSlash === true) {\n        filteredOptions.spaceBeforeSlash = ' ';\n      }\n      filteredOptions.suppressPrettyCount = 0;\n      filteredOptions.user = {};\n      filteredOptions.state = WriterState.None;\n      return filteredOptions;\n    };\n\n    XMLWriterBase.prototype.indent = function(node, options, level) {\n      var indentLevel;\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else if (options.pretty) {\n        indentLevel = (level || 0) + options.offset + 1;\n        if (indentLevel > 0) {\n          return new Array(indentLevel).join(options.indent);\n        }\n      }\n      return '';\n    };\n\n    XMLWriterBase.prototype.endline = function(node, options, level) {\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else {\n        return options.newline;\n      }\n    };\n\n    XMLWriterBase.prototype.attribute = function(att, options, level) {\n      var r;\n      this.openAttribute(att, options, level);\n      r = ' ' + att.name + '=\"' + att.value + '\"';\n      this.closeAttribute(att, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.cdata = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<![CDATA[';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ']]>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.comment = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!-- ';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ' -->' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.declaration = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?xml';\n      options.state = WriterState.InsideTag;\n      r += ' version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.docType = function(node, options, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += ']';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, i, j, len, len1, name, prettySuppressed, r, ref, ref1, ref2;\n      level || (level = 0);\n      prettySuppressed = false;\n      r = '';\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r += this.indent(node, options, level) + '<' + node.name;\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          r += '>';\n          options.state = WriterState.CloseTag;\n          r += '</' + node.name + '>' + this.endline(node, options, level);\n        } else {\n          options.state = WriterState.CloseTag;\n          r += options.spaceBeforeSlash + '/>' + this.endline(node, options, level);\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        r += '>';\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        r += this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        r += '</' + node.name + '>' + this.endline(node, options, level);\n      } else {\n        if (options.dontPrettyTextNodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if ((child.type === NodeType.Text || child.type === NodeType.Raw) && (child.value != null)) {\n              options.suppressPrettyCount++;\n              prettySuppressed = true;\n              break;\n            }\n          }\n        }\n        r += '>' + this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += this.indent(node, options, level) + '</' + node.name + '>';\n        if (prettySuppressed) {\n          options.suppressPrettyCount--;\n        }\n        r += this.endline(node, options, level);\n        options.state = WriterState.None;\n      }\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.writeChildNode = function(node, options, level) {\n      switch (node.type) {\n        case NodeType.CData:\n          return this.cdata(node, options, level);\n        case NodeType.Comment:\n          return this.comment(node, options, level);\n        case NodeType.Element:\n          return this.element(node, options, level);\n        case NodeType.Raw:\n          return this.raw(node, options, level);\n        case NodeType.Text:\n          return this.text(node, options, level);\n        case NodeType.ProcessingInstruction:\n          return this.processingInstruction(node, options, level);\n        case NodeType.Dummy:\n          return '';\n        case NodeType.Declaration:\n          return this.declaration(node, options, level);\n        case NodeType.DocType:\n          return this.docType(node, options, level);\n        case NodeType.AttributeDeclaration:\n          return this.dtdAttList(node, options, level);\n        case NodeType.ElementDeclaration:\n          return this.dtdElement(node, options, level);\n        case NodeType.EntityDeclaration:\n          return this.dtdEntity(node, options, level);\n        case NodeType.NotationDeclaration:\n          return this.dtdNotation(node, options, level);\n        default:\n          throw new Error(\"Unknown XML node type: \" + node.constructor.name);\n      }\n    };\n\n    XMLWriterBase.prototype.processingInstruction = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?';\n      options.state = WriterState.InsideTag;\n      r += node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.raw = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.text = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdAttList = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ATTLIST';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdElement = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ELEMENT';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name + ' ' + node.value;\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdEntity = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ENTITY';\n      options.state = WriterState.InsideTag;\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdNotation = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!NOTATION';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.openNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.closeNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.openAttribute = function(att, options, level) {};\n\n    XMLWriterBase.prototype.closeAttribute = function(att, options, level) {};\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLWriterBase.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/index.js ***!
  \***********************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/Utility.js\"), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLDocumentCB = __webpack_require__(/*! ./XMLDocumentCB */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLDocumentCB.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  XMLStreamWriter = __webpack_require__(/*! ./XMLStreamWriter */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/XMLStreamWriter.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/NodeType.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n  module.exports.implementation = new XMLDOMImplementation();\n\n  module.exports.nodeType = NodeType;\n\n  module.exports.writerState = WriterState;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/xmlbuilder@11.0.1/node_modules/xmlbuilder/lib/index.js\n");

/***/ })

};
;