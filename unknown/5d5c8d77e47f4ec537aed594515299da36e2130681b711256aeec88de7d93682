"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-toast@1.2.6_18606f2f2c37bce3f11ae18feba9ee94";
exports.ids = ["vendor-chunks/@radix-ui+react-toast@1.2.6_18606f2f2c37bce3f11ae18feba9ee94"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_18606f2f2c37bce3f11ae18feba9ee94/node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_18606f2f2c37bce3f11ae18feba9ee94/node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_f0ca6b954f28db1d942bcdb5b2347248/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_fe5e3e67b6d7a8cbcdac508a3412d002/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._f09ca44b075894bef1f22048f2189343/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._f6427c6d64d2d542f3fe67e5d488b7cd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_4171a71166cfa012d93ab3a234657034/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_df97a09d62161aad15439954749cb418/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_89c4c6402d5b83b03c66d469fb85e060/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_96e5d54b7bd10cf53cadcc132d77f938/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // packages/react/toast/src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open = true, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.6_18606f2f2c37bce3f11ae18feba9ee94/node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ })

};
;