/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/screenfull@5.2.0";
exports.ids = ["vendor-chunks/screenfull@5.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js ***!
  \****************************************************************************************/
/***/ ((module) => {

eval("/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) Sindre Sorhus; MIT License\n*/\n(function () {\n\t'use strict';\n\n\tvar document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n\tvar isCommonjs =  true && module.exports;\n\n\tvar fn = (function () {\n\t\tvar val;\n\n\t\tvar fnMap = [\n\t\t\t[\n\t\t\t\t'requestFullscreen',\n\t\t\t\t'exitFullscreen',\n\t\t\t\t'fullscreenElement',\n\t\t\t\t'fullscreenEnabled',\n\t\t\t\t'fullscreenchange',\n\t\t\t\t'fullscreenerror'\n\t\t\t],\n\t\t\t// New WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullscreen',\n\t\t\t\t'webkitExitFullscreen',\n\t\t\t\t'webkitFullscreenElement',\n\t\t\t\t'webkitFullscreenEnabled',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t// Old WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullScreen',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitCurrentFullScreenElement',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t[\n\t\t\t\t'mozRequestFullScreen',\n\t\t\t\t'mozCancelFullScreen',\n\t\t\t\t'mozFullScreenElement',\n\t\t\t\t'mozFullScreenEnabled',\n\t\t\t\t'mozfullscreenchange',\n\t\t\t\t'mozfullscreenerror'\n\t\t\t],\n\t\t\t[\n\t\t\t\t'msRequestFullscreen',\n\t\t\t\t'msExitFullscreen',\n\t\t\t\t'msFullscreenElement',\n\t\t\t\t'msFullscreenEnabled',\n\t\t\t\t'MSFullscreenChange',\n\t\t\t\t'MSFullscreenError'\n\t\t\t]\n\t\t];\n\n\t\tvar i = 0;\n\t\tvar l = fnMap.length;\n\t\tvar ret = {};\n\n\t\tfor (; i < l; i++) {\n\t\t\tval = fnMap[i];\n\t\t\tif (val && val[1] in document) {\n\t\t\t\tfor (i = 0; i < val.length; i++) {\n\t\t\t\t\tret[fnMap[0][i]] = val[i];\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t})();\n\n\tvar eventNameMap = {\n\t\tchange: fn.fullscreenchange,\n\t\terror: fn.fullscreenerror\n\t};\n\n\tvar screenfull = {\n\t\trequest: function (element, options) {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tvar onFullScreenEntered = function () {\n\t\t\t\t\tthis.off('change', onFullScreenEntered);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenEntered);\n\n\t\t\t\telement = element || document.documentElement;\n\n\t\t\t\tvar returnPromise = element[fn.requestFullscreen](options);\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\texit: function () {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tif (!this.isFullscreen) {\n\t\t\t\t\tresolve();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar onFullScreenExit = function () {\n\t\t\t\t\tthis.off('change', onFullScreenExit);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenExit);\n\n\t\t\t\tvar returnPromise = document[fn.exitFullscreen]();\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\ttoggle: function (element, options) {\n\t\t\treturn this.isFullscreen ? this.exit() : this.request(element, options);\n\t\t},\n\t\tonchange: function (callback) {\n\t\t\tthis.on('change', callback);\n\t\t},\n\t\tonerror: function (callback) {\n\t\t\tthis.on('error', callback);\n\t\t},\n\t\ton: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\toff: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\traw: fn\n\t};\n\n\tif (!fn) {\n\t\tif (isCommonjs) {\n\t\t\tmodule.exports = {isEnabled: false};\n\t\t} else {\n\t\t\twindow.screenfull = {isEnabled: false};\n\t\t}\n\n\t\treturn;\n\t}\n\n\tObject.defineProperties(screenfull, {\n\t\tisFullscreen: {\n\t\t\tget: function () {\n\t\t\t\treturn Boolean(document[fn.fullscreenElement]);\n\t\t\t}\n\t\t},\n\t\telement: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\treturn document[fn.fullscreenElement];\n\t\t\t}\n\t\t},\n\t\tisEnabled: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\t// Coerce to boolean in case of old WebKit\n\t\t\t\treturn Boolean(document[fn.fullscreenEnabled]);\n\t\t\t}\n\t\t}\n\t});\n\n\tif (isCommonjs) {\n\t\tmodule.exports = screenfull;\n\t} else {\n\t\twindow.screenfull = screenfull;\n\t}\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js\n");

/***/ })

};
;