"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@userback+react@0.3.7_react_06296e1f31d6f38d5831dedcc857be82";
exports.ids = ["vendor-chunks/@userback+react@0.3.7_react_06296e1f31d6f38d5831dedcc857be82"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@userback+react@0.3.7_react_06296e1f31d6f38d5831dedcc857be82/node_modules/@userback/react/dist/react.mjs":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@userback+react@0.3.7_react_06296e1f31d6f38d5831dedcc857be82/node_modules/@userback/react/dist/react.mjs ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserbackProvider: () => (/* binding */ UserbackProvider),\n/* harmony export */   useUserback: () => (/* binding */ useUserback),\n/* harmony export */   useUserbackContext: () => (/* binding */ useUserbackContext),\n/* harmony export */   withUserback: () => (/* binding */ withUserback)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _userback_widget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @userback/widget */ \"(ssr)/./node_modules/.pnpm/@userback+widget@0.3.7/node_modules/@userback/widget/dist/widget.mjs\");\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nconst UserbackContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n/**\n * UserbackProider\n *\n * @example `<UserbackProvider token={UB_TOKEN} ><MyRouter /></UserbackProvider>`\n * @returns React.Component\n */\nconst UserbackProvider = ({ token, options = {}, widgetSettings: widget_settings, delayInit = false, children, }) => {\n    const ubLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const [Userback, setUserback] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((_token = token, _options = Object.assign({ widget_settings }, options)) => __awaiter(void 0, void 0, void 0, function* () {\n        if (Userback)\n            return Userback;\n        ubLoaded.current = true;\n        const ub = yield (0,_userback_widget__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_token, _options);\n        setUserback(ub);\n        return ub;\n    }), [Userback, token, widget_settings, options]);\n    // onMount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ubLoaded.current && !delayInit) {\n            init(token, Object.assign({ widget_settings }, options));\n        }\n    }, [delayInit]); // eslint-disable-line react-hooks/exhaustive-deps\n    // Api hooks\n    const hide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.hide(); }, [Userback]);\n    const show = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.show(); }, [Userback]);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.close(); }, [Userback]);\n    const openPortal = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.openPortal(); }, [Userback]);\n    const isLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => (Userback === null || Userback === void 0 ? void 0 : Userback.isLoaded()) || false, [Userback]);\n    const setName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name) => { Userback === null || Userback === void 0 ? void 0 : Userback.setName(name); }, [Userback]);\n    const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((data) => { Userback === null || Userback === void 0 ? void 0 : Userback.setData(data); }, [Userback]);\n    const setEmail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((email) => { Userback === null || Userback === void 0 ? void 0 : Userback.setEmail(email); }, [Userback]);\n    const setCategories = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((categories) => { Userback === null || Userback === void 0 ? void 0 : Userback.setCategories(categories); }, [Userback]);\n    const setPriority = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((priority) => { Userback === null || Userback === void 0 ? void 0 : Userback.setPriority(priority); }, [Userback]);\n    const addHeader = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key, value) => { Userback === null || Userback === void 0 ? void 0 : Userback.addHeader(key, value); }, [Userback]);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((feedback, destination) => {\n        Userback === null || Userback === void 0 ? void 0 : Userback.open(feedback, destination);\n    }, [Userback]);\n    const destroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        Userback === null || Userback === void 0 ? void 0 : Userback.destroy();\n        setUserback(undefined);\n        ubLoaded.current = false;\n    }, [Userback]);\n    const identify = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((user_id, user_info) => Userback === null || Userback === void 0 ? void 0 : Userback.identify(user_id, user_info), [Userback]);\n    const openSurvey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key) => Userback === null || Userback === void 0 ? void 0 : Userback.openSurvey(key), [Userback]);\n    const closeSurvey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.closeSurvey(), [Userback]);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.refresh(), [Userback]);\n    // Create the provider values, usable upstream by users\n    const providerValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        init,\n        show,\n        hide,\n        open,\n        close,\n        destroy,\n        setData,\n        setEmail,\n        setCategories,\n        setPriority,\n        addHeader,\n        identify,\n        openPortal,\n        isLoaded,\n        setName,\n        openSurvey,\n        closeSurvey,\n        refresh,\n    }), [\n        init,\n        show,\n        hide,\n        open,\n        close,\n        destroy,\n        setData,\n        setEmail,\n        setCategories,\n        setPriority,\n        addHeader,\n        identify,\n        openPortal,\n        isLoaded,\n        setName,\n        openSurvey,\n        closeSurvey,\n        refresh,\n    ]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserbackContext.Provider, { value: providerValue }, children));\n};\nconst useUserbackContext = () => {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UserbackContext);\n    if (ctx === undefined) {\n        throw new Error('`useUserback` must be used within `UserbackProvider`.');\n    }\n    return ctx;\n};\n/* Provides the Userback api as React hooks */\nconst useUserback = () => useUserbackContext();\n/**\n * A higher Ordered Component for using hooks within a class based component\n * */\nfunction withUserback(Component) {\n    return function UserbackWrapper(props) {\n        const userback = useUserback();\n        // eslint-disable-next-line react/jsx-props-no-spreading\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, Object.assign({}, props, { userback: userback })));\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@userback+react@0.3.7_react_06296e1f31d6f38d5831dedcc857be82/node_modules/@userback/react/dist/react.mjs\n");

/***/ })

};
;