"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-accordion@1_2f292b7e22e9e96fd519d3d15739bb41";
exports.ids = ["vendor-chunks/@radix-ui+react-accordion@1_2f292b7e22e9e96fd519d3d15739bb41"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-accordion@1_2f292b7e22e9e96fd519d3d15739bb41/node_modules/@radix-ui/react-accordion/dist/index.mjs":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-accordion@1_2f292b7e22e9e96fd519d3d15739bb41/node_modules/@radix-ui/react-accordion/dist/index.mjs ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* binding */ Accordion),\n/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),\n/* harmony export */   AccordionHeader: () => (/* binding */ AccordionHeader),\n/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),\n/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger2),\n/* harmony export */   createAccordionScope: () => (/* binding */ createAccordionScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_f0ca6b954f28db1d942bcdb5b2347248/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_df97a09d62161aad15439954749cb418/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_b50db4bf24efeedd3a7854f67ade7a60/node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_5826504d8f5d72f5a896ce83b922c98e/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Accordion,AccordionContent,AccordionHeader,AccordionItem,AccordionTrigger,Content,Header,Item,Root,Trigger,createAccordionScope auto */ // packages/react/accordion/src/accordion.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\n    \"Home\",\n    \"End\",\n    \"ArrowDown\",\n    \"ArrowUp\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(ACCORDION_NAME, [\n    createCollectionScope,\n    _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope\n]);\nvar useCollapsibleScope = (0,_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope)();\nvar Accordion = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeAccordion,\n        children: type === \"multiple\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplMultiple, {\n            ...multipleProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplSingle, {\n            ...singleProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(ACCORDION_NAME, {\n    collapsible: false\n});\nvar AccordionImplSingle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, collapsible = false, ...accordionSingleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value: value ? [\n            value\n        ] : [],\n        onItemOpen: setValue,\n        onItemClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>collapsible && setValue(\"\"), [\n            collapsible,\n            setValue\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionSingleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar AccordionImplMultiple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, ...accordionMultipleProps } = props;\n    const [value = [], setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const handleItemOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>setValue((prevValue = [])=>[\n                ...prevValue,\n                itemValue\n            ]), [\n        setValue\n    ]);\n    const handleItemClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>setValue((prevValue = [])=>prevValue.filter((value2)=>value2 !== itemValue)), [\n        setValue\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value,\n        onItemOpen: handleItemOpen,\n        onItemClose: handleItemClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionMultipleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n        if (!ACCORDION_KEYS.includes(event.key)) return;\n        const target = event.target;\n        const triggerCollection = getItems().filter((item)=>!item.ref.current?.disabled);\n        const triggerIndex = triggerCollection.findIndex((item)=>item.ref.current === target);\n        const triggerCount = triggerCollection.length;\n        if (triggerIndex === -1) return;\n        event.preventDefault();\n        let nextIndex = triggerIndex;\n        const homeIndex = 0;\n        const endIndex = triggerCount - 1;\n        const moveNext = ()=>{\n            nextIndex = triggerIndex + 1;\n            if (nextIndex > endIndex) {\n                nextIndex = homeIndex;\n            }\n        };\n        const movePrev = ()=>{\n            nextIndex = triggerIndex - 1;\n            if (nextIndex < homeIndex) {\n                nextIndex = endIndex;\n            }\n        };\n        switch(event.key){\n            case \"Home\":\n                nextIndex = homeIndex;\n                break;\n            case \"End\":\n                nextIndex = endIndex;\n                break;\n            case \"ArrowRight\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        moveNext();\n                    } else {\n                        movePrev();\n                    }\n                }\n                break;\n            case \"ArrowDown\":\n                if (orientation === \"vertical\") {\n                    moveNext();\n                }\n                break;\n            case \"ArrowLeft\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        movePrev();\n                    } else {\n                        moveNext();\n                    }\n                }\n                break;\n            case \"ArrowUp\":\n                if (orientation === \"vertical\") {\n                    movePrev();\n                }\n                break;\n        }\n        const clampedIndex = nextIndex % triggerCount;\n        triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplProvider, {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: __scopeAccordion,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.div, {\n                ...accordionProps,\n                \"data-orientation\": orientation,\n                ref: composedRefs,\n                onKeyDown: disabled ? void 0 : handleKeyDown\n            })\n        })\n    });\n});\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionItemProvider, {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Root, {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2)=>{\n                if (open2) {\n                    valueContext.onItemOpen(value);\n                } else {\n                    valueContext.onItemClose(value);\n                }\n            }\n        })\n    });\n});\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.h3, {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n    });\n});\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeAccordion,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n            \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n            \"data-orientation\": accordionContext.orientation,\n            id: itemContext.triggerId,\n            ...collapsibleScope,\n            ...triggerProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n            [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n            [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n            ...props.style\n        }\n    });\n});\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-accordion@1_2f292b7e22e9e96fd519d3d15739bb41/node_modules/@radix-ui/react-accordion/dist/index.mjs\n");

/***/ })

};
;