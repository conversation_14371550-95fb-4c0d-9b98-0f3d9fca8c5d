"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@mantine+hooks@8.0.0_react@18.3.1";
exports.ids = ["vendor-chunks/@mantine+hooks@8.0.0_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@mantine+hooks@8.0.0_react@18.3.1/node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@mantine+hooks@8.0.0_react@18.3.1/node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ useMediaQuery auto */ \nfunction attachMediaListener(query, callback) {\n    try {\n        query.addEventListener(\"change\", callback);\n        return ()=>query.removeEventListener(\"change\", callback);\n    } catch (e) {\n        query.addListener(callback);\n        return ()=>query.removeListener(callback);\n    }\n}\nfunction getInitialValue(query, initialValue) {\n    if (false) {}\n    return false;\n}\nfunction useMediaQuery(query, initialValue, { getInitialValueInEffect } = {\n    getInitialValueInEffect: true\n}) {\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialValueInEffect ? initialValue : getInitialValue(query));\n    const queryRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (\"matchMedia\" in window) {\n            queryRef.current = window.matchMedia(query);\n            setMatches(queryRef.current.matches);\n            return attachMediaListener(queryRef.current, (event)=>setMatches(event.matches));\n        }\n        return void 0;\n    }, [\n        query\n    ]);\n    return matches;\n}\n //# sourceMappingURL=use-media-query.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@mantine+hooks@8.0.0_react@18.3.1/node_modules/@mantine/hooks/esm/use-media-query/use-media-query.mjs\n");

/***/ })

};
;