"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql@16.10.0";
exports.ids = ["vendor-chunks/graphql@16.10.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/GraphQLError.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/GraphQLError.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   printError: () => (/* binding */ printError)\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/GraphQLError.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/syntaxError.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/syntaxError.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   syntaxError: () => (/* binding */ syntaxError)\n/* harmony export */ });\n/* harmony import */ var _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GraphQLError.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/GraphQLError.mjs\");\n\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nfunction syntaxError(source, position, description) {\n  return new _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position],\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2Vycm9yL3N5bnRheEVycm9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQLGFBQWEsMkRBQVksa0JBQWtCLFlBQVk7QUFDdkQ7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2Vycm9yL3N5bnRheEVycm9yLm1qcz9mNmQ4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdyYXBoUUxFcnJvciB9IGZyb20gJy4vR3JhcGhRTEVycm9yLm1qcyc7XG4vKipcbiAqIFByb2R1Y2VzIGEgR3JhcGhRTEVycm9yIHJlcHJlc2VudGluZyBhIHN5bnRheCBlcnJvciwgY29udGFpbmluZyB1c2VmdWxcbiAqIGRlc2NyaXB0aXZlIGluZm9ybWF0aW9uIGFib3V0IHRoZSBzeW50YXggZXJyb3IncyBwb3NpdGlvbiBpbiB0aGUgc291cmNlLlxuICovXG5cbmV4cG9ydCBmdW5jdGlvbiBzeW50YXhFcnJvcihzb3VyY2UsIHBvc2l0aW9uLCBkZXNjcmlwdGlvbikge1xuICByZXR1cm4gbmV3IEdyYXBoUUxFcnJvcihgU3ludGF4IEVycm9yOiAke2Rlc2NyaXB0aW9ufWAsIHtcbiAgICBzb3VyY2UsXG4gICAgcG9zaXRpb25zOiBbcG9zaXRpb25dLFxuICB9KTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/syntaxError.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/devAssert.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/devAssert.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   devAssert: () => (/* binding */ devAssert)\n/* harmony export */ });\nfunction devAssert(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(message);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvZGV2QXNzZXJ0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvZGV2QXNzZXJ0Lm1qcz82NzE1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBkZXZBc3NlcnQoY29uZGl0aW9uLCBtZXNzYWdlKSB7XG4gIGNvbnN0IGJvb2xlYW5Db25kaXRpb24gPSBCb29sZWFuKGNvbmRpdGlvbik7XG5cbiAgaWYgKCFib29sZWFuQ29uZGl0aW9uKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/devAssert.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inspect: () => (/* binding */ inspect)\n/* harmony export */ });\nconst MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nfunction inspect(value) {\n  return formatValue(value, []);\n}\n\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n\n    case 'object':\n      return formatObjectValue(value, seenValues);\n\n    default:\n      return String(value);\n  }\n}\n\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n\n  const seenValues = [...previouslySeenValues, value];\n\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string'\n        ? jsonValue\n        : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n\n  return formatObject(value, seenValues);\n}\n\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\n\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n\n  if (entries.length === 0) {\n    return '{}';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n\n  const properties = entries.map(\n    ([key, value]) => key + ': ' + formatValue(value, seenValues),\n  );\n  return '{ ' + properties.join(', ') + ' }';\n}\n\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n\n  return '[' + items.join(', ') + ']';\n}\n\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString\n    .call(object)\n    .replace(/^\\[object /, '')\n    .replace(/]$/, '');\n\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n\n  return tag;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/instanceOf.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/instanceOf.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   instanceOf: () => (/* binding */ instanceOf)\n/* harmony export */ });\n/* harmony import */ var _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inspect.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs\");\n\n/* c8 ignore next 3 */\n\nconst isProduction =\n  globalThis.process && // eslint-disable-next-line no-undef\n  \"development\" === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nconst instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  isProduction\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = (0,_inspect_mjs__WEBPACK_IMPORTED_MODULE_0__.inspect)(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/instanceOf.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/invariant.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/invariant.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant)\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaW52YXJpYW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2dyYXBocWxAMTYuMTAuMC9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2ludmFyaWFudC5tanM/MTU1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaW52YXJpYW50KGNvbmRpdGlvbiwgbWVzc2FnZSkge1xuICBjb25zdCBib29sZWFuQ29uZGl0aW9uID0gQm9vbGVhbihjb25kaXRpb24pO1xuXG4gIGlmICghYm9vbGVhbkNvbmRpdGlvbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIG1lc3NhZ2UgIT0gbnVsbCA/IG1lc3NhZ2UgOiAnVW5leHBlY3RlZCBpbnZhcmlhbnQgdHJpZ2dlcmVkLicsXG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/invariant.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/isObjectLike.mjs":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike)\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaXNPYmplY3RMaWtlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2dyYXBocWxAMTYuMTAuMC9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2lzT2JqZWN0TGlrZS5tanM/ODYxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJldHVybiB0cnVlIGlmIGB2YWx1ZWAgaXMgb2JqZWN0LWxpa2UuIEEgdmFsdWUgaXMgb2JqZWN0LWxpa2UgaWYgaXQncyBub3RcbiAqIGBudWxsYCBhbmQgaGFzIGEgYHR5cGVvZmAgcmVzdWx0IG9mIFwib2JqZWN0XCIuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc09iamVjdExpa2UodmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PSAnb2JqZWN0JyAmJiB2YWx1ZSAhPT0gbnVsbDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/isObjectLike.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Location: () => (/* binding */ Location),\n/* harmony export */   OperationTypeNode: () => (/* binding */ OperationTypeNode),\n/* harmony export */   QueryDocumentKeys: () => (/* binding */ QueryDocumentKeys),\n/* harmony export */   Token: () => (/* binding */ Token),\n/* harmony export */   isNode: () => (/* binding */ isNode)\n/* harmony export */ });\n/**\n * Contains a range of UTF-8 character offsets and token references that\n * identify the region of the source from which the AST derived.\n */\nclass Location {\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The Token at which this Node begins.\n   */\n\n  /**\n   * The Token at which this Node ends.\n   */\n\n  /**\n   * The Source document the AST represents.\n   */\n  constructor(startToken, endToken, source) {\n    this.start = startToken.start;\n    this.end = endToken.end;\n    this.startToken = startToken;\n    this.endToken = endToken;\n    this.source = source;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Location';\n  }\n\n  toJSON() {\n    return {\n      start: this.start,\n      end: this.end,\n    };\n  }\n}\n/**\n * Represents a range of characters represented by a lexical token\n * within a Source.\n */\n\nclass Token {\n  /**\n   * The kind of Token.\n   */\n\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The 1-indexed line number on which this Token appears.\n   */\n\n  /**\n   * The 1-indexed column number at which this Token begins.\n   */\n\n  /**\n   * For non-punctuation tokens, represents the interpreted value of the token.\n   *\n   * Note: is undefined for punctuation tokens, but typed as string for\n   * convenience in the parser.\n   */\n\n  /**\n   * Tokens exist as nodes in a double-linked-list amongst all tokens\n   * including ignored tokens. <SOF> is always the first node and <EOF>\n   * the last.\n   */\n  constructor(kind, start, end, line, column, value) {\n    this.kind = kind;\n    this.start = start;\n    this.end = end;\n    this.line = line;\n    this.column = column; // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    this.value = value;\n    this.prev = null;\n    this.next = null;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Token';\n  }\n\n  toJSON() {\n    return {\n      kind: this.kind,\n      value: this.value,\n      line: this.line,\n      column: this.column,\n    };\n  }\n}\n/**\n * The list of all possible AST node types.\n */\n\n/**\n * @internal\n */\nconst QueryDocumentKeys = {\n  Name: [],\n  Document: ['definitions'],\n  OperationDefinition: [\n    'name',\n    'variableDefinitions',\n    'directives',\n    'selectionSet',\n  ],\n  VariableDefinition: ['variable', 'type', 'defaultValue', 'directives'],\n  Variable: ['name'],\n  SelectionSet: ['selections'],\n  Field: ['alias', 'name', 'arguments', 'directives', 'selectionSet'],\n  Argument: ['name', 'value'],\n  FragmentSpread: ['name', 'directives'],\n  InlineFragment: ['typeCondition', 'directives', 'selectionSet'],\n  FragmentDefinition: [\n    'name', // Note: fragment variable definitions are deprecated and will removed in v17.0.0\n    'variableDefinitions',\n    'typeCondition',\n    'directives',\n    'selectionSet',\n  ],\n  IntValue: [],\n  FloatValue: [],\n  StringValue: [],\n  BooleanValue: [],\n  NullValue: [],\n  EnumValue: [],\n  ListValue: ['values'],\n  ObjectValue: ['fields'],\n  ObjectField: ['name', 'value'],\n  Directive: ['name', 'arguments'],\n  NamedType: ['name'],\n  ListType: ['type'],\n  NonNullType: ['type'],\n  SchemaDefinition: ['description', 'directives', 'operationTypes'],\n  OperationTypeDefinition: ['type'],\n  ScalarTypeDefinition: ['description', 'name', 'directives'],\n  ObjectTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  FieldDefinition: ['description', 'name', 'arguments', 'type', 'directives'],\n  InputValueDefinition: [\n    'description',\n    'name',\n    'type',\n    'defaultValue',\n    'directives',\n  ],\n  InterfaceTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  UnionTypeDefinition: ['description', 'name', 'directives', 'types'],\n  EnumTypeDefinition: ['description', 'name', 'directives', 'values'],\n  EnumValueDefinition: ['description', 'name', 'directives'],\n  InputObjectTypeDefinition: ['description', 'name', 'directives', 'fields'],\n  DirectiveDefinition: ['description', 'name', 'arguments', 'locations'],\n  SchemaExtension: ['directives', 'operationTypes'],\n  ScalarTypeExtension: ['name', 'directives'],\n  ObjectTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  InterfaceTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  UnionTypeExtension: ['name', 'directives', 'types'],\n  EnumTypeExtension: ['name', 'directives', 'values'],\n  InputObjectTypeExtension: ['name', 'directives', 'fields'],\n};\nconst kindValues = new Set(Object.keys(QueryDocumentKeys));\n/**\n * @internal\n */\n\nfunction isNode(maybeNode) {\n  const maybeKind =\n    maybeNode === null || maybeNode === void 0 ? void 0 : maybeNode.kind;\n  return typeof maybeKind === 'string' && kindValues.has(maybeKind);\n}\n/** Name */\n\nvar OperationTypeNode;\n\n(function (OperationTypeNode) {\n  OperationTypeNode['QUERY'] = 'query';\n  OperationTypeNode['MUTATION'] = 'mutation';\n  OperationTypeNode['SUBSCRIPTION'] = 'subscription';\n})(OperationTypeNode || (OperationTypeNode = {}));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/blockString.mjs":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/blockString.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedentBlockStringLines: () => (/* binding */ dedentBlockStringLines),\n/* harmony export */   isPrintableAsBlockString: () => (/* binding */ isPrintableAsBlockString),\n/* harmony export */   printBlockString: () => (/* binding */ printBlockString)\n/* harmony export */ });\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/characterClasses.mjs\");\n\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nfunction dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine =\n      (_firstNonEmptyLine = firstNonEmptyLine) !== null &&\n      _firstNonEmptyLine !== void 0\n        ? _firstNonEmptyLine\n        : i;\n    lastNonEmptyLine = i;\n\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n\n  return lines // Remove common indentation from all lines but first.\n    .map((line, i) => (i === 0 ? line : line.slice(commonIndent))) // Remove leading and trailing blank lines.\n    .slice(\n      (_firstNonEmptyLine2 = firstNonEmptyLine) !== null &&\n        _firstNonEmptyLine2 !== void 0\n        ? _firstNonEmptyLine2\n        : 0,\n      lastNonEmptyLine + 1,\n    );\n}\n\nfunction leadingWhitespace(str) {\n  let i = 0;\n\n  while (i < str.length && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(str.charCodeAt(i))) {\n    ++i;\n  }\n\n  return i;\n}\n/**\n * @internal\n */\n\nfunction isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nfunction printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine =\n    lines.length > 1 &&\n    lines\n      .slice(1)\n      .every((line) => line.length === 0 || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines =\n    !(options !== null && options !== void 0 && options.minimize) && // add leading and trailing new lines only if it improves readability\n    (!isSingleLine ||\n      value.length > 70 ||\n      forceTrailingNewline ||\n      forceLeadingNewLine ||\n      hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(value.charCodeAt(0));\n\n  if ((printAsMultipleLines && !skipLeadingNewLine) || forceLeadingNewLine) {\n    result += '\\n';\n  }\n\n  result += escapedValue;\n\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n\n  return '\"\"\"' + result + '\"\"\"';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/blockString.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/characterClasses.mjs":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/characterClasses.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDigit: () => (/* binding */ isDigit),\n/* harmony export */   isLetter: () => (/* binding */ isLetter),\n/* harmony export */   isNameContinue: () => (/* binding */ isNameContinue),\n/* harmony export */   isNameStart: () => (/* binding */ isNameStart),\n/* harmony export */   isWhiteSpace: () => (/* binding */ isWhiteSpace)\n/* harmony export */ });\n/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nfunction isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nfunction isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nfunction isLetter(code) {\n  return (\n    (code >= 0x0061 && code <= 0x007a) || // A-Z\n    (code >= 0x0041 && code <= 0x005a) // a-z\n  );\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/characterClasses.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/directiveLocation.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/directiveLocation.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectiveLocation: () => (/* binding */ DirectiveLocation)\n/* harmony export */ });\n/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\n\n\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/directiveLocation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Kind: () => (/* binding */ Kind)\n/* harmony export */ });\n/**\n * The set of allowed kind values for AST nodes.\n */\nvar Kind;\n\n(function (Kind) {\n  Kind['NAME'] = 'Name';\n  Kind['DOCUMENT'] = 'Document';\n  Kind['OPERATION_DEFINITION'] = 'OperationDefinition';\n  Kind['VARIABLE_DEFINITION'] = 'VariableDefinition';\n  Kind['SELECTION_SET'] = 'SelectionSet';\n  Kind['FIELD'] = 'Field';\n  Kind['ARGUMENT'] = 'Argument';\n  Kind['FRAGMENT_SPREAD'] = 'FragmentSpread';\n  Kind['INLINE_FRAGMENT'] = 'InlineFragment';\n  Kind['FRAGMENT_DEFINITION'] = 'FragmentDefinition';\n  Kind['VARIABLE'] = 'Variable';\n  Kind['INT'] = 'IntValue';\n  Kind['FLOAT'] = 'FloatValue';\n  Kind['STRING'] = 'StringValue';\n  Kind['BOOLEAN'] = 'BooleanValue';\n  Kind['NULL'] = 'NullValue';\n  Kind['ENUM'] = 'EnumValue';\n  Kind['LIST'] = 'ListValue';\n  Kind['OBJECT'] = 'ObjectValue';\n  Kind['OBJECT_FIELD'] = 'ObjectField';\n  Kind['DIRECTIVE'] = 'Directive';\n  Kind['NAMED_TYPE'] = 'NamedType';\n  Kind['LIST_TYPE'] = 'ListType';\n  Kind['NON_NULL_TYPE'] = 'NonNullType';\n  Kind['SCHEMA_DEFINITION'] = 'SchemaDefinition';\n  Kind['OPERATION_TYPE_DEFINITION'] = 'OperationTypeDefinition';\n  Kind['SCALAR_TYPE_DEFINITION'] = 'ScalarTypeDefinition';\n  Kind['OBJECT_TYPE_DEFINITION'] = 'ObjectTypeDefinition';\n  Kind['FIELD_DEFINITION'] = 'FieldDefinition';\n  Kind['INPUT_VALUE_DEFINITION'] = 'InputValueDefinition';\n  Kind['INTERFACE_TYPE_DEFINITION'] = 'InterfaceTypeDefinition';\n  Kind['UNION_TYPE_DEFINITION'] = 'UnionTypeDefinition';\n  Kind['ENUM_TYPE_DEFINITION'] = 'EnumTypeDefinition';\n  Kind['ENUM_VALUE_DEFINITION'] = 'EnumValueDefinition';\n  Kind['INPUT_OBJECT_TYPE_DEFINITION'] = 'InputObjectTypeDefinition';\n  Kind['DIRECTIVE_DEFINITION'] = 'DirectiveDefinition';\n  Kind['SCHEMA_EXTENSION'] = 'SchemaExtension';\n  Kind['SCALAR_TYPE_EXTENSION'] = 'ScalarTypeExtension';\n  Kind['OBJECT_TYPE_EXTENSION'] = 'ObjectTypeExtension';\n  Kind['INTERFACE_TYPE_EXTENSION'] = 'InterfaceTypeExtension';\n  Kind['UNION_TYPE_EXTENSION'] = 'UnionTypeExtension';\n  Kind['ENUM_TYPE_EXTENSION'] = 'EnumTypeExtension';\n  Kind['INPUT_OBJECT_TYPE_EXTENSION'] = 'InputObjectTypeExtension';\n})(Kind || (Kind = {}));\n\n\n/**\n * The enum type representing the possible kind values of AST nodes.\n *\n * @deprecated Please use `Kind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/lexer.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/lexer.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lexer: () => (/* binding */ Lexer),\n/* harmony export */   isPunctuatorTokenKind: () => (/* binding */ isPunctuatorTokenKind)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blockString.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/characterClasses.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nclass Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = (this.token = this.lookahead());\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT);\n    }\n\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nfunction isPunctuatorTokenKind(kind) {\n  return (\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R\n  );\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return (\n    (code >= 0x0000 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0x10ffff)\n  );\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return (\n    isLeadingSurrogate(body.charCodeAt(location)) &&\n    isTrailingSurrogate(body.charCodeAt(location + 1))\n  );\n}\n\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\n\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n\n  if (code === undefined) {\n    return _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG, position, position + 1);\n\n      case 0x0024:\n        // $\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR, position, position + 1);\n\n      case 0x0026:\n        // &\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP, position, position + 1);\n\n      case 0x0028:\n        // (\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L, position, position + 1);\n\n      case 0x0029:\n        // )\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R, position, position + 1);\n\n      case 0x002e:\n        // .\n        if (\n          body.charCodeAt(position + 1) === 0x002e &&\n          body.charCodeAt(position + 2) === 0x002e\n        ) {\n          return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD, position, position + 3);\n        }\n\n        break;\n\n      case 0x003a:\n        // :\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON, position, position + 1);\n\n      case 0x003d:\n        // =\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS, position, position + 1);\n\n      case 0x0040:\n        // @\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT, position, position + 1);\n\n      case 0x005b:\n        // [\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L, position, position + 1);\n\n      case 0x005d:\n        // ]\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R, position, position + 1);\n\n      case 0x007b:\n        // {\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L, position, position + 1);\n\n      case 0x007c:\n        // |\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE, position, position + 1);\n\n      case 0x007d:\n        // }\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (\n          body.charCodeAt(position + 1) === 0x0022 &&\n          body.charCodeAt(position + 2) === 0x0022\n        ) {\n          return readBlockString(lexer, position);\n        }\n\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n      return readName(lexer, position);\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      code === 0x0027\n        ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?'\n        : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position)\n        ? `Unexpected character: ${printCodePointAt(lexer, position)}.`\n        : `Invalid character: ${printCodePointAt(lexer, position)}.`,\n    );\n  }\n\n  return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT,\n    start,\n    position,\n    body.slice(start + 1, position),\n  );\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code)) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid number, unexpected digit after 0: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        position,\n      )}.`,\n    );\n  }\n\n  return createToken(\n    lexer,\n    isFloat ? _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.FLOAT : _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.INT,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!(0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(firstCode)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      start,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        start,\n      )}.`,\n    );\n  }\n\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(body.charCodeAt(position))) {\n    ++position;\n  }\n\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape =\n        body.charCodeAt(position + 1) === 0x0075 // u\n          ? body.charCodeAt(position + 2) === 0x007b // {\n            ? readEscapedUnicodeVariableWidth(lexer, position)\n            : readEscapedUnicodeFixedWidth(lexer, position)\n          : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n\n      return {\n        value: String.fromCodePoint(point),\n        size,\n      };\n    } // Append this hex digit to the code point.\n\n    point = (point << 4) | readHexDigit(code);\n\n    if (point < 0) {\n      break;\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(\n      position,\n      position + size,\n    )}\".`,\n  );\n}\n\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6,\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (\n      body.charCodeAt(position + 6) === 0x005c &&\n      body.charCodeAt(position + 7) === 0x0075\n    ) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12,\n        };\n      }\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`,\n  );\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return (\n    (readHexDigit(body.charCodeAt(position)) << 12) |\n    (readHexDigit(body.charCodeAt(position + 1)) << 8) |\n    (readHexDigit(body.charCodeAt(position + 2)) << 4) |\n    readHexDigit(body.charCodeAt(position + 3))\n  );\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n    ? code - 0x0030\n    : code >= 0x0041 && code <= 0x0046 // A-F\n    ? code - 0x0037\n    : code >= 0x0061 && code <= 0x0066 // a-f\n    ? code - 0x0057\n    : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2,\n      };\n\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2,\n      };\n\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2,\n      };\n\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2,\n      };\n\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2,\n      };\n\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2,\n      };\n\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2,\n      };\n\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2,\n      };\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid character escape sequence: \"${body.slice(\n      position,\n      position + 2,\n    )}\".`,\n  );\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (\n      code === 0x0022 &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(\n        lexer,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BLOCK_STRING,\n        start,\n        position + 3, // Return a string of the lines joined with U+000A.\n        (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_4__.dedentBlockStringLines)(blockLines).join('\\n'),\n      );\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (\n      code === 0x005c &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022 &&\n      body.charCodeAt(position + 3) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameContinue)(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.NAME,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/lexer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/location.mjs":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/location.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: () => (/* binding */ getLocation)\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMC4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2xvY2F0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxpRUFBUzs7QUFFaEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9ncmFwaHFsQDE2LjEwLjAvbm9kZV9tb2R1bGVzL2dyYXBocWwvbGFuZ3VhZ2UvbG9jYXRpb24ubWpzPzI2YzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnLi4vanN1dGlscy9pbnZhcmlhbnQubWpzJztcbmNvbnN0IExpbmVSZWdFeHAgPSAvXFxyXFxufFtcXG5cXHJdL2c7XG4vKipcbiAqIFJlcHJlc2VudHMgYSBsb2NhdGlvbiBpbiBhIFNvdXJjZS5cbiAqL1xuXG4vKipcbiAqIFRha2VzIGEgU291cmNlIGFuZCBhIFVURi04IGNoYXJhY3RlciBvZmZzZXQsIGFuZCByZXR1cm5zIHRoZSBjb3JyZXNwb25kaW5nXG4gKiBsaW5lIGFuZCBjb2x1bW4gYXMgYSBTb3VyY2VMb2NhdGlvbi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldExvY2F0aW9uKHNvdXJjZSwgcG9zaXRpb24pIHtcbiAgbGV0IGxhc3RMaW5lU3RhcnQgPSAwO1xuICBsZXQgbGluZSA9IDE7XG5cbiAgZm9yIChjb25zdCBtYXRjaCBvZiBzb3VyY2UuYm9keS5tYXRjaEFsbChMaW5lUmVnRXhwKSkge1xuICAgIHR5cGVvZiBtYXRjaC5pbmRleCA9PT0gJ251bWJlcicgfHwgaW52YXJpYW50KGZhbHNlKTtcblxuICAgIGlmIChtYXRjaC5pbmRleCA+PSBwb3NpdGlvbikge1xuICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgbGFzdExpbmVTdGFydCA9IG1hdGNoLmluZGV4ICsgbWF0Y2hbMF0ubGVuZ3RoO1xuICAgIGxpbmUgKz0gMTtcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgbGluZSxcbiAgICBjb2x1bW46IHBvc2l0aW9uICsgMSAtIGxhc3RMaW5lU3RhcnQsXG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/location.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/parser.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/parser.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseConstValue: () => (/* binding */ parseConstValue),\n/* harmony export */   parseType: () => (/* binding */ parseType),\n/* harmony export */   parseValue: () => (/* binding */ parseValue)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ast.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./directiveLocation.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/directiveLocation.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./kinds.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexer.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/lexer.mjs\");\n/* harmony import */ var _source_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./source.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/source.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n\n\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nfunction parse(source, options) {\n  const parser = new Parser(source, options);\n  const document = parser.parseDocument();\n  Object.defineProperty(document, 'tokenCount', {\n    enumerable: false,\n    value: parser.tokenCount,\n  });\n  return document;\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nfunction parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nfunction parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nfunction parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nclass Parser {\n  constructor(source, options = {}) {\n    const sourceObj = (0,_source_mjs__WEBPACK_IMPORTED_MODULE_1__.isSource)(source) ? source : new _source_mjs__WEBPACK_IMPORTED_MODULE_1__.Source(source);\n    this._lexer = new _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n\n  get tokenCount() {\n    return this._tokenCounter;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAME,\n      value: token.value,\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DOCUMENT,\n      definitions: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF,\n        this.parseDefinition,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF,\n      ),\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription\n      ? this._lexer.lookahead()\n      : this._lexer.token;\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n\n      if (hasDescription) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          this._lexer.token.start,\n          'Unexpected description, descriptions are supported only on type definitions.',\n        );\n      }\n\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n        operation: _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    const operation = this.parseOperationType();\n    let name;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY;\n\n      case 'mutation':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.MUTATION;\n\n      case 'subscription':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.SUBSCRIPTION;\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseVariableDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n        ? this.parseConstValueLiteral()\n        : undefined,\n      directives: this.parseConstDirectives(),\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE,\n      name: this.parseName(),\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SELECTION_SET,\n      selections: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n        this.parseSelection,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n      ),\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD)\n      ? this.parseFragment()\n      : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)\n        ? this.parseSelectionSet()\n        : undefined,\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  }\n\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n\n    switch (token.kind) {\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FLOAT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING:\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME:\n        this.advanceLexer();\n\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: true,\n            });\n\n          case 'false':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: false,\n            });\n\n          case 'null':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NULL,\n            });\n\n          default:\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM,\n              value: token.value,\n            });\n        }\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n\n          if (this._lexer.token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n              this._lexer.source,\n              token.start,\n              `Unexpected variable \"$${varName}\" in constant value.`,\n            );\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n\n        return this.parseVariable();\n\n      default:\n        throw this.unexpected();\n    }\n  }\n\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.STRING,\n      value: token.value,\n      block: token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING,\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST,\n      values: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R),\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT,\n      fields: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R),\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n\n    while (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST_TYPE,\n        type: innerType,\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BANG)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NON_NULL_TYPE,\n        type,\n      });\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAMED_TYPE,\n      name: this.parseName(),\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING) || this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type,\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements')\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AMP, this.parseNamedType)\n      : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseFieldDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives,\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives,\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseNamedType)\n      : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseEnumValueDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (\n      this._lexer.token.value === 'true' ||\n      this._lexer.token.value === 'false' ||\n      this._lexer.token.value === 'null'\n    ) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        this._lexer.token.start,\n        `${getTokenDesc(\n          this._lexer.token,\n        )} is reserved and cannot be used for an enum value.`,\n      );\n    }\n\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations,\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n\n    if (Object.prototype.hasOwnProperty.call(_directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__.DirectiveLocation, name.value)) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.Location(\n        startToken,\n        this._lexer.lastToken,\n        this._lexer.source,\n      );\n    }\n\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        token.start,\n        `Expected \"${value}\", found ${getTokenDesc(token)}.`,\n      );\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token =\n      atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Unexpected ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  }\n\n  advanceLexer() {\n    const { maxTokens } = this._options;\n\n    const token = this._lexer.advance();\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF) {\n      ++this._tokenCounter;\n\n      if (maxTokens !== undefined && this._tokenCounter > maxTokens) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          token.start,\n          `Document contains more that ${maxTokens} tokens. Parsing aborted.`,\n        );\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return (0,_lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPunctuatorTokenKind)(kind) ? `\"${kind}\"` : kind;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/parser.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/predicates.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/predicates.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isConstValueNode: () => (/* binding */ isConstValueNode),\n/* harmony export */   isDefinitionNode: () => (/* binding */ isDefinitionNode),\n/* harmony export */   isExecutableDefinitionNode: () => (/* binding */ isExecutableDefinitionNode),\n/* harmony export */   isSelectionNode: () => (/* binding */ isSelectionNode),\n/* harmony export */   isTypeDefinitionNode: () => (/* binding */ isTypeDefinitionNode),\n/* harmony export */   isTypeExtensionNode: () => (/* binding */ isTypeExtensionNode),\n/* harmony export */   isTypeNode: () => (/* binding */ isTypeNode),\n/* harmony export */   isTypeSystemDefinitionNode: () => (/* binding */ isTypeSystemDefinitionNode),\n/* harmony export */   isTypeSystemExtensionNode: () => (/* binding */ isTypeSystemExtensionNode),\n/* harmony export */   isValueNode: () => (/* binding */ isValueNode)\n/* harmony export */ });\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./kinds.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs\");\n\nfunction isDefinitionNode(node) {\n  return (\n    isExecutableDefinitionNode(node) ||\n    isTypeSystemDefinitionNode(node) ||\n    isTypeSystemExtensionNode(node)\n  );\n}\nfunction isExecutableDefinitionNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.OPERATION_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.FRAGMENT_DEFINITION\n  );\n}\nfunction isSelectionNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.FIELD ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.FRAGMENT_SPREAD ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INLINE_FRAGMENT\n  );\n}\nfunction isValueNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.VARIABLE ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INT ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.FLOAT ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.STRING ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.BOOLEAN ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.NULL ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.ENUM ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.LIST ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.OBJECT\n  );\n}\nfunction isConstValueNode(node) {\n  return (\n    isValueNode(node) &&\n    (node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.LIST\n      ? node.values.some(isConstValueNode)\n      : node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.OBJECT\n      ? node.fields.some((field) => isConstValueNode(field.value))\n      : node.kind !== _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.VARIABLE)\n  );\n}\nfunction isTypeNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.NAMED_TYPE ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.LIST_TYPE ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.NON_NULL_TYPE\n  );\n}\nfunction isTypeSystemDefinitionNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.SCHEMA_DEFINITION ||\n    isTypeDefinitionNode(node) ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.DIRECTIVE_DEFINITION\n  );\n}\nfunction isTypeDefinitionNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.SCALAR_TYPE_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.OBJECT_TYPE_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INTERFACE_TYPE_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.UNION_TYPE_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.ENUM_TYPE_DEFINITION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INPUT_OBJECT_TYPE_DEFINITION\n  );\n}\nfunction isTypeSystemExtensionNode(node) {\n  return node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.SCHEMA_EXTENSION || isTypeExtensionNode(node);\n}\nfunction isTypeExtensionNode(node) {\n  return (\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.SCALAR_TYPE_EXTENSION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.OBJECT_TYPE_EXTENSION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INTERFACE_TYPE_EXTENSION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.UNION_TYPE_EXTENSION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.ENUM_TYPE_EXTENSION ||\n    node.kind === _kinds_mjs__WEBPACK_IMPORTED_MODULE_0__.Kind.INPUT_OBJECT_TYPE_EXTENSION\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/predicates.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printLocation.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printLocation.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printLocation: () => (/* binding */ printLocation),\n/* harmony export */   printSourceLocation: () => (/* binding */ printSourceLocation)\n/* harmony export */ });\n/* harmony import */ var _location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./location.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/location.mjs\");\n\n\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\nfunction printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    (0,_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nfunction printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printLocation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printString.mjs":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printString.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printString: () => (/* binding */ printString)\n/* harmony export */ });\n/**\n * Prints a string as a GraphQL StringValue literal. Replaces control characters\n * and excluded characters (\" U+0022 and \\\\ U+005C) with escape sequences.\n */\nfunction printString(str) {\n  return `\"${str.replace(escapedRegExp, escapedReplacer)}\"`;\n} // eslint-disable-next-line no-control-regex\n\nconst escapedRegExp = /[\\x00-\\x1f\\x22\\x5c\\x7f-\\x9f]/g;\n\nfunction escapedReplacer(str) {\n  return escapeSequences[str.charCodeAt(0)];\n} // prettier-ignore\n\nconst escapeSequences = [\n  '\\\\u0000',\n  '\\\\u0001',\n  '\\\\u0002',\n  '\\\\u0003',\n  '\\\\u0004',\n  '\\\\u0005',\n  '\\\\u0006',\n  '\\\\u0007',\n  '\\\\b',\n  '\\\\t',\n  '\\\\n',\n  '\\\\u000B',\n  '\\\\f',\n  '\\\\r',\n  '\\\\u000E',\n  '\\\\u000F',\n  '\\\\u0010',\n  '\\\\u0011',\n  '\\\\u0012',\n  '\\\\u0013',\n  '\\\\u0014',\n  '\\\\u0015',\n  '\\\\u0016',\n  '\\\\u0017',\n  '\\\\u0018',\n  '\\\\u0019',\n  '\\\\u001A',\n  '\\\\u001B',\n  '\\\\u001C',\n  '\\\\u001D',\n  '\\\\u001E',\n  '\\\\u001F',\n  '',\n  '',\n  '\\\\\"',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 2F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 3F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 4F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\\\\\',\n  '',\n  '',\n  '', // 5F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 6F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\u007F',\n  '\\\\u0080',\n  '\\\\u0081',\n  '\\\\u0082',\n  '\\\\u0083',\n  '\\\\u0084',\n  '\\\\u0085',\n  '\\\\u0086',\n  '\\\\u0087',\n  '\\\\u0088',\n  '\\\\u0089',\n  '\\\\u008A',\n  '\\\\u008B',\n  '\\\\u008C',\n  '\\\\u008D',\n  '\\\\u008E',\n  '\\\\u008F',\n  '\\\\u0090',\n  '\\\\u0091',\n  '\\\\u0092',\n  '\\\\u0093',\n  '\\\\u0094',\n  '\\\\u0095',\n  '\\\\u0096',\n  '\\\\u0097',\n  '\\\\u0098',\n  '\\\\u0099',\n  '\\\\u009A',\n  '\\\\u009B',\n  '\\\\u009C',\n  '\\\\u009D',\n  '\\\\u009E',\n  '\\\\u009F',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printString.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printer.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printer.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   print: () => (/* binding */ print)\n/* harmony export */ });\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blockString.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _printString_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./printString.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printString.mjs\");\n/* harmony import */ var _visitor_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./visitor.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/visitor.mjs\");\n\n\n\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\n\nfunction print(ast) {\n  return (0,_visitor_mjs__WEBPACK_IMPORTED_MODULE_0__.visit)(ast, printDocASTReducer);\n}\nconst MAX_LINE_LENGTH = 80;\nconst printDocASTReducer = {\n  Name: {\n    leave: (node) => node.value,\n  },\n  Variable: {\n    leave: (node) => '$' + node.name,\n  },\n  // Document\n  Document: {\n    leave: (node) => join(node.definitions, '\\n\\n'),\n  },\n  OperationDefinition: {\n    leave(node) {\n      const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n      const prefix = join(\n        [\n          node.operation,\n          join([node.name, varDefs]),\n          join(node.directives, ' '),\n        ],\n        ' ',\n      ); // Anonymous queries with no directives or variable definitions can use\n      // the query short form.\n\n      return (prefix === 'query' ? '' : prefix + ' ') + node.selectionSet;\n    },\n  },\n  VariableDefinition: {\n    leave: ({ variable, type, defaultValue, directives }) =>\n      variable +\n      ': ' +\n      type +\n      wrap(' = ', defaultValue) +\n      wrap(' ', join(directives, ' ')),\n  },\n  SelectionSet: {\n    leave: ({ selections }) => block(selections),\n  },\n  Field: {\n    leave({ alias, name, arguments: args, directives, selectionSet }) {\n      const prefix = wrap('', alias, ': ') + name;\n      let argsLine = prefix + wrap('(', join(args, ', '), ')');\n\n      if (argsLine.length > MAX_LINE_LENGTH) {\n        argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n      }\n\n      return join([argsLine, join(directives, ' '), selectionSet], ' ');\n    },\n  },\n  Argument: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Fragments\n  FragmentSpread: {\n    leave: ({ name, directives }) =>\n      '...' + name + wrap(' ', join(directives, ' ')),\n  },\n  InlineFragment: {\n    leave: ({ typeCondition, directives, selectionSet }) =>\n      join(\n        [\n          '...',\n          wrap('on ', typeCondition),\n          join(directives, ' '),\n          selectionSet,\n        ],\n        ' ',\n      ),\n  },\n  FragmentDefinition: {\n    leave: (\n      { name, typeCondition, variableDefinitions, directives, selectionSet }, // Note: fragment variable definitions are experimental and may be changed\n    ) =>\n      // or removed in the future.\n      `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n      `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n      selectionSet,\n  },\n  // Value\n  IntValue: {\n    leave: ({ value }) => value,\n  },\n  FloatValue: {\n    leave: ({ value }) => value,\n  },\n  StringValue: {\n    leave: ({ value, block: isBlockString }) =>\n      isBlockString ? (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_1__.printBlockString)(value) : (0,_printString_mjs__WEBPACK_IMPORTED_MODULE_2__.printString)(value),\n  },\n  BooleanValue: {\n    leave: ({ value }) => (value ? 'true' : 'false'),\n  },\n  NullValue: {\n    leave: () => 'null',\n  },\n  EnumValue: {\n    leave: ({ value }) => value,\n  },\n  ListValue: {\n    leave: ({ values }) => '[' + join(values, ', ') + ']',\n  },\n  ObjectValue: {\n    leave: ({ fields }) => '{' + join(fields, ', ') + '}',\n  },\n  ObjectField: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Directive\n  Directive: {\n    leave: ({ name, arguments: args }) =>\n      '@' + name + wrap('(', join(args, ', '), ')'),\n  },\n  // Type\n  NamedType: {\n    leave: ({ name }) => name,\n  },\n  ListType: {\n    leave: ({ type }) => '[' + type + ']',\n  },\n  NonNullType: {\n    leave: ({ type }) => type + '!',\n  },\n  // Type System Definitions\n  SchemaDefinition: {\n    leave: ({ description, directives, operationTypes }) =>\n      wrap('', description, '\\n') +\n      join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n  },\n  OperationTypeDefinition: {\n    leave: ({ operation, type }) => operation + ': ' + type,\n  },\n  ScalarTypeDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') +\n      join(['scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  FieldDefinition: {\n    leave: ({ description, name, arguments: args, type, directives }) =>\n      wrap('', description, '\\n') +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      ': ' +\n      type +\n      wrap(' ', join(directives, ' ')),\n  },\n  InputValueDefinition: {\n    leave: ({ description, name, type, defaultValue, directives }) =>\n      wrap('', description, '\\n') +\n      join(\n        [name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')],\n        ' ',\n      ),\n  },\n  InterfaceTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeDefinition: {\n    leave: ({ description, name, directives, types }) =>\n      wrap('', description, '\\n') +\n      join(\n        ['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))],\n        ' ',\n      ),\n  },\n  EnumTypeDefinition: {\n    leave: ({ description, name, directives, values }) =>\n      wrap('', description, '\\n') +\n      join(['enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  EnumValueDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') + join([name, join(directives, ' ')], ' '),\n  },\n  InputObjectTypeDefinition: {\n    leave: ({ description, name, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(['input', name, join(directives, ' '), block(fields)], ' '),\n  },\n  DirectiveDefinition: {\n    leave: ({ description, name, arguments: args, repeatable, locations }) =>\n      wrap('', description, '\\n') +\n      'directive @' +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      (repeatable ? ' repeatable' : '') +\n      ' on ' +\n      join(locations, ' | '),\n  },\n  SchemaExtension: {\n    leave: ({ directives, operationTypes }) =>\n      join(\n        ['extend schema', join(directives, ' '), block(operationTypes)],\n        ' ',\n      ),\n  },\n  ScalarTypeExtension: {\n    leave: ({ name, directives }) =>\n      join(['extend scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  InterfaceTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeExtension: {\n    leave: ({ name, directives, types }) =>\n      join(\n        [\n          'extend union',\n          name,\n          join(directives, ' '),\n          wrap('= ', join(types, ' | ')),\n        ],\n        ' ',\n      ),\n  },\n  EnumTypeExtension: {\n    leave: ({ name, directives, values }) =>\n      join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  InputObjectTypeExtension: {\n    leave: ({ name, directives, fields }) =>\n      join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n  },\n};\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\n\nfunction join(maybeArray, separator = '') {\n  var _maybeArray$filter$jo;\n\n  return (_maybeArray$filter$jo =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.filter((x) => x).join(separator)) !== null &&\n    _maybeArray$filter$jo !== void 0\n    ? _maybeArray$filter$jo\n    : '';\n}\n/**\n * Given array, print each item on its own line, wrapped in an indented `{ }` block.\n */\n\nfunction block(array) {\n  return wrap('{\\n', indent(join(array, '\\n')), '\\n}');\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise print an empty string.\n */\n\nfunction wrap(start, maybeString, end = '') {\n  return maybeString != null && maybeString !== ''\n    ? start + maybeString + end\n    : '';\n}\n\nfunction indent(str) {\n  return wrap('  ', str.replace(/\\n/g, '\\n  '));\n}\n\nfunction hasMultilineItems(maybeArray) {\n  var _maybeArray$some;\n\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n  return (_maybeArray$some =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.some((str) => str.includes('\\n'))) !== null &&\n    _maybeArray$some !== void 0\n    ? _maybeArray$some\n    : false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/printer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/source.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/source.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Source: () => (/* binding */ Source),\n/* harmony export */   isSource: () => (/* binding */ isSource)\n/* harmony export */ });\n/* harmony import */ var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/devAssert.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/devAssert.mjs\");\n/* harmony import */ var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/inspect.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs\");\n/* harmony import */ var _jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../jsutils/instanceOf.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/instanceOf.mjs\");\n\n\n\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nclass Source {\n  constructor(\n    body,\n    name = 'GraphQL request',\n    locationOffset = {\n      line: 1,\n      column: 1,\n    },\n  ) {\n    typeof body === 'string' ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(false, `Body must be a string. Received: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__.inspect)(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'line in locationOffset is 1-indexed and must be positive.',\n      );\n    this.locationOffset.column > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'column in locationOffset is 1-indexed and must be positive.',\n      );\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nfunction isSource(source) {\n  return (0,_jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__.instanceOf)(source, Source);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/source.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/tokenKind.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/tokenKind.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenKind: () => (/* binding */ TokenKind)\n/* harmony export */ });\n/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind;\n\n(function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n})(TokenKind || (TokenKind = {}));\n\n\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/tokenKind.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/visitor.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/visitor.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAK: () => (/* binding */ BREAK),\n/* harmony export */   getEnterLeaveForKind: () => (/* binding */ getEnterLeaveForKind),\n/* harmony export */   getVisitFn: () => (/* binding */ getVisitFn),\n/* harmony export */   visit: () => (/* binding */ visit),\n/* harmony export */   visitInParallel: () => (/* binding */ visitInParallel)\n/* harmony export */ });\n/* harmony import */ var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../jsutils/devAssert.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/devAssert.mjs\");\n/* harmony import */ var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../jsutils/inspect.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/jsutils/inspect.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kinds.mjs */ \"(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/kinds.mjs\");\n\n\n\n\n/**\n * A visitor is provided to visit, it contains the collection of\n * relevant functions to be called during the visitor's traversal.\n */\n\nconst BREAK = Object.freeze({});\n/**\n * visit() will walk through an AST using a depth-first traversal, calling\n * the visitor's enter function at each node in the traversal, and calling the\n * leave function after visiting that node and all of its child nodes.\n *\n * By returning different values from the enter and leave functions, the\n * behavior of the visitor can be altered, including skipping over a sub-tree of\n * the AST (by returning false), editing the AST by returning a value or null\n * to remove the value, or to stop the whole traversal by returning BREAK.\n *\n * When using visit() to edit an AST, the original AST will not be modified, and\n * a new version of the AST with the changes applied will be returned from the\n * visit function.\n *\n * ```ts\n * const editedAST = visit(ast, {\n *   enter(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: skip visiting this node\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   },\n *   leave(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: no action\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   }\n * });\n * ```\n *\n * Alternatively to providing enter() and leave() functions, a visitor can\n * instead provide functions named the same as the kinds of AST nodes, or\n * enter/leave visitors at a named key, leading to three permutations of the\n * visitor API:\n *\n * 1) Named visitors triggered when entering a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind(node) {\n *     // enter the \"Kind\" node\n *   }\n * })\n * ```\n *\n * 2) Named visitors that trigger upon entering and leaving a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind: {\n *     enter(node) {\n *       // enter the \"Kind\" node\n *     }\n *     leave(node) {\n *       // leave the \"Kind\" node\n *     }\n *   }\n * })\n * ```\n *\n * 3) Generic visitors that trigger upon entering and leaving any node.\n *\n * ```ts\n * visit(ast, {\n *   enter(node) {\n *     // enter any node\n *   },\n *   leave(node) {\n *     // leave any node\n *   }\n * })\n * ```\n */\n\nfunction visit(root, visitor, visitorKeys = _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.QueryDocumentKeys) {\n  const enterLeaveMap = new Map();\n\n  for (const kind of Object.values(_kinds_mjs__WEBPACK_IMPORTED_MODULE_1__.Kind)) {\n    enterLeaveMap.set(kind, getEnterLeaveForKind(visitor, kind));\n  }\n  /* eslint-disable no-undef-init */\n\n  let stack = undefined;\n  let inArray = Array.isArray(root);\n  let keys = [root];\n  let index = -1;\n  let edits = [];\n  let node = root;\n  let key = undefined;\n  let parent = undefined;\n  const path = [];\n  const ancestors = [];\n  /* eslint-enable no-undef-init */\n\n  do {\n    index++;\n    const isLeaving = index === keys.length;\n    const isEdited = isLeaving && edits.length !== 0;\n\n    if (isLeaving) {\n      key = ancestors.length === 0 ? undefined : path[path.length - 1];\n      node = parent;\n      parent = ancestors.pop();\n\n      if (isEdited) {\n        if (inArray) {\n          node = node.slice();\n          let editOffset = 0;\n\n          for (const [editKey, editValue] of edits) {\n            const arrayKey = editKey - editOffset;\n\n            if (editValue === null) {\n              node.splice(arrayKey, 1);\n              editOffset++;\n            } else {\n              node[arrayKey] = editValue;\n            }\n          }\n        } else {\n          node = Object.defineProperties(\n            {},\n            Object.getOwnPropertyDescriptors(node),\n          );\n\n          for (const [editKey, editValue] of edits) {\n            node[editKey] = editValue;\n          }\n        }\n      }\n\n      index = stack.index;\n      keys = stack.keys;\n      edits = stack.edits;\n      inArray = stack.inArray;\n      stack = stack.prev;\n    } else if (parent) {\n      key = inArray ? index : keys[index];\n      node = parent[key];\n\n      if (node === null || node === undefined) {\n        continue;\n      }\n\n      path.push(key);\n    }\n\n    let result;\n\n    if (!Array.isArray(node)) {\n      var _enterLeaveMap$get, _enterLeaveMap$get2;\n\n      (0,_ast_mjs__WEBPACK_IMPORTED_MODULE_0__.isNode)(node) || (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_2__.devAssert)(false, `Invalid AST Node: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_3__.inspect)(node)}.`);\n      const visitFn = isLeaving\n        ? (_enterLeaveMap$get = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get === void 0\n          ? void 0\n          : _enterLeaveMap$get.leave\n        : (_enterLeaveMap$get2 = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get2 === void 0\n        ? void 0\n        : _enterLeaveMap$get2.enter;\n      result =\n        visitFn === null || visitFn === void 0\n          ? void 0\n          : visitFn.call(visitor, node, key, parent, path, ancestors);\n\n      if (result === BREAK) {\n        break;\n      }\n\n      if (result === false) {\n        if (!isLeaving) {\n          path.pop();\n          continue;\n        }\n      } else if (result !== undefined) {\n        edits.push([key, result]);\n\n        if (!isLeaving) {\n          if ((0,_ast_mjs__WEBPACK_IMPORTED_MODULE_0__.isNode)(result)) {\n            node = result;\n          } else {\n            path.pop();\n            continue;\n          }\n        }\n      }\n    }\n\n    if (result === undefined && isEdited) {\n      edits.push([key, node]);\n    }\n\n    if (isLeaving) {\n      path.pop();\n    } else {\n      var _node$kind;\n\n      stack = {\n        inArray,\n        index,\n        keys,\n        edits,\n        prev: stack,\n      };\n      inArray = Array.isArray(node);\n      keys = inArray\n        ? node\n        : (_node$kind = visitorKeys[node.kind]) !== null &&\n          _node$kind !== void 0\n        ? _node$kind\n        : [];\n      index = -1;\n      edits = [];\n\n      if (parent) {\n        ancestors.push(parent);\n      }\n\n      parent = node;\n    }\n  } while (stack !== undefined);\n\n  if (edits.length !== 0) {\n    // New root\n    return edits[edits.length - 1][1];\n  }\n\n  return root;\n}\n/**\n * Creates a new visitor instance which delegates to many visitors to run in\n * parallel. Each visitor will be visited for each node before moving on.\n *\n * If a prior visitor edits a node, no following visitors will see that node.\n */\n\nfunction visitInParallel(visitors) {\n  const skipping = new Array(visitors.length).fill(null);\n  const mergedVisitor = Object.create(null);\n\n  for (const kind of Object.values(_kinds_mjs__WEBPACK_IMPORTED_MODULE_1__.Kind)) {\n    let hasVisitor = false;\n    const enterList = new Array(visitors.length).fill(undefined);\n    const leaveList = new Array(visitors.length).fill(undefined);\n\n    for (let i = 0; i < visitors.length; ++i) {\n      const { enter, leave } = getEnterLeaveForKind(visitors[i], kind);\n      hasVisitor || (hasVisitor = enter != null || leave != null);\n      enterList[i] = enter;\n      leaveList[i] = leave;\n    }\n\n    if (!hasVisitor) {\n      continue;\n    }\n\n    const mergedEnterLeave = {\n      enter(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _enterList$i;\n\n            const result =\n              (_enterList$i = enterList[i]) === null || _enterList$i === void 0\n                ? void 0\n                : _enterList$i.apply(visitors[i], args);\n\n            if (result === false) {\n              skipping[i] = node;\n            } else if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined) {\n              return result;\n            }\n          }\n        }\n      },\n\n      leave(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _leaveList$i;\n\n            const result =\n              (_leaveList$i = leaveList[i]) === null || _leaveList$i === void 0\n                ? void 0\n                : _leaveList$i.apply(visitors[i], args);\n\n            if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined && result !== false) {\n              return result;\n            }\n          } else if (skipping[i] === node) {\n            skipping[i] = null;\n          }\n        }\n      },\n    };\n    mergedVisitor[kind] = mergedEnterLeave;\n  }\n\n  return mergedVisitor;\n}\n/**\n * Given a visitor instance and a node kind, return EnterLeaveVisitor for that kind.\n */\n\nfunction getEnterLeaveForKind(visitor, kind) {\n  const kindVisitor = visitor[kind];\n\n  if (typeof kindVisitor === 'object') {\n    // { Kind: { enter() {}, leave() {} } }\n    return kindVisitor;\n  } else if (typeof kindVisitor === 'function') {\n    // { Kind() {} }\n    return {\n      enter: kindVisitor,\n      leave: undefined,\n    };\n  } // { enter() {}, leave() {} }\n\n  return {\n    enter: visitor.enter,\n    leave: visitor.leave,\n  };\n}\n/**\n * Given a visitor instance, if it is leaving or not, and a node kind, return\n * the function the visitor runtime should call.\n *\n * @deprecated Please use `getEnterLeaveForKind` instead. Will be removed in v17\n */\n\n/* c8 ignore next 8 */\n\nfunction getVisitFn(visitor, kind, isLeaving) {\n  const { enter, leave } = getEnterLeaveForKind(visitor, kind);\n  return isLeaving ? leave : enter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/graphql@16.10.0/node_modules/graphql/language/visitor.mjs\n");

/***/ })

};
;