'use client'

import React, { useRef, useState, Drag<PERSON><PERSON>, ChangeEvent, ReactNode } from 'react'
import Image from 'next/image'
import { Loader2 } from 'lucide-react'

import { Input } from '@/components/ui/input'
import { cn } from '@/app/lib/utils'

// Base file interface that can be extended
export interface BaseFile {
    title: string
    [key: string]: any
}

// Props for the base FileUploadUI component
export interface FileUploadUIProps<T extends BaseFile> {
    // Required props
    files: T[]
    onFilesSelected: (files: FileList) => void
    
    // Optional UI customization
    text?: string
    subText?: string
    bgClass?: string
    multipleUpload?: boolean
    acceptedFileTypes?: string
    
    // Loading state
    isLoading?: boolean
    
    // File display customization
    renderFileItem?: (file: T, index: number) => ReactNode
    onFileClick?: (file: T) => void
    
    // Additional content
    children?: ReactNode
}

export default function FileUploadUI<T extends BaseFile>({
    files,
    onFilesSelected,
    text = 'Documents and Images',
    subText,
    bgClass = '',
    multipleUpload = true,
    acceptedFileTypes = '.xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv',
    isLoading = false,
    renderFileItem,
    onFileClick,
    children,
}: FileUploadUIProps<T>) {
    /* ------------------------------------------------------- */
    /* state / refs                                            */
    /* ------------------------------------------------------- */
    const [dragActive, setDragActive] = useState<boolean>(false)
    const inputRef = useRef<HTMLInputElement>(null)

    /* ------------------------------------------------------- */
    /* helpers                                                 */
    /* ------------------------------------------------------- */
    const dropZoneClasses = cn(
        'relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none',
        dragActive ? 'bg-accent border-primary' : 'bg-accent/50 border-border',
        'text-foreground hover:bg-accent hover:border-primary',
        'min-h-[10rem] cursor-pointer select-none',
        bgClass,
    )

    /* ------------------------------------------------------- */
    /* event handlers                                          */
    /* ------------------------------------------------------- */
    const handleFiles = (fileList: FileList) => {
        onFilesSelected(fileList)
    }

    const onChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) handleFiles(e.target.files)
    }

    const onDrop = (e: DragEvent<HTMLFormElement>) => {
        e.preventDefault()
        setDragActive(false)
        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files)
    }

    const onDragToggle = (state: boolean) => (e: DragEvent) => {
        e.preventDefault()
        setDragActive(state)
    }

    const openFileExplorer = () => {
        if (inputRef.current) {
            inputRef.current.value = ''
            inputRef.current.click()
        }
    }

    /* ------------------------------------------------------- */
    /* default file item renderer                              */
    /* ------------------------------------------------------- */
    const defaultRenderFileItem = (file: T, index: number) => (
        <div
            key={index}
            onClick={() => onFileClick?.(file)}
            className="flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden">
            <Image
                src="/sealogs-document_upload.svg"
                alt="Document"
                width={48}
                height={48}
                className="mb-2"
            />
            <div className="text-xs text-center break-all text-muted-foreground">
                {file.title}
            </div>
        </div>
    )

    /* ------------------------------------------------------- */
    /* render                                                  */
    /* ------------------------------------------------------- */
    return (
        <div className="w-full pt-4 lg:pt-0">
            {/* uploaded files display */}
            {files.length > 0 && (
                <div className="flex flex-wrap gap-4 mb-4">
                    {files
                        .filter((file: T) => file.title?.length > 0)
                        .map((file: T, index: number) => 
                            renderFileItem 
                                ? renderFileItem(file, index)
                                : defaultRenderFileItem(file, index)
                        )}
                </div>
            )}

            {/* upload form */}
            <form
                className={dropZoneClasses}
                onSubmit={(e) => e.preventDefault()}
                onDragEnter={onDragToggle(true)}
                onDragOver={onDragToggle(true)}
                onDragLeave={onDragToggle(false)}
                onDrop={onDrop}
                onClick={openFileExplorer}
                aria-label="File uploader drop zone">
                {/* heading */}
                <span className="absolute top-4 left-4 text-xs font-medium uppercase tracking-wider">
                    {text}
                </span>

                {/* hidden native input */}
                <Input
                    ref={inputRef}
                    type="file"
                    className="hidden"
                    multiple={multipleUpload}
                    accept={acceptedFileTypes}
                    onChange={onChange}
                />

                {/* interactive area */}
                <div className="flex flex-col items-center gap-2 pointer-events-none">
                    <Image
                        src="/sealogs-document_upload.svg"
                        alt="Upload illustration"
                        width={96}
                        height={96}
                        className="relative -translate-x-2.5"
                        priority
                    />
                    {subText && (
                        <span className="text-sm font-medium text-neutral-400">
                            {subText}
                        </span>
                    )}
                </div>
            </form>

            {/* loading state */}
            {isLoading && (
                <div className="mt-4 flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">
                        Uploading...
                    </span>
                </div>
            )}

            {/* additional content */}
            {children}
        </div>
    )
}
