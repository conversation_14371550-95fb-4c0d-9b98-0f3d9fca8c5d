"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc";
exports.ids = ["vendor-chunks/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc/node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc/node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._f6427c6d64d2d542f3fe67e5d488b7cd/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_4171a71166cfa012d93ab3a234657034/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_5826504d8f5d72f5a896ce83b922c98e/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_89c4c6402d5b83b03c66d469fb85e060/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // packages/react/scroll-area/src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// packages/react/scroll-area/src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/scroll-area/src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar?.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = void 0;\n        }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, ()=>{\n        const height2 = context.scrollbarX?.offsetHeight || 0;\n        context.onCornerHeightChange(height2);\n        setHeight(height2);\n    });\n    useResizeObserver(context.scrollbarY, ()=>{\n        const width2 = context.scrollbarY?.offsetWidth || 0;\n        context.onCornerWidthChange(width2);\n        setWidth(width2);\n    });\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-scroll-area_951b2c98b871c6cc4614653d3a853dcc/node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ })

};
;