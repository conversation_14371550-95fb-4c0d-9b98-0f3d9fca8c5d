"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.1._c56d354b83a56e6a914687c302946fec";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.1._c56d354b83a56e6a914687c302946fec"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._c56d354b83a56e6a914687c302946fec/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.1._c56d354b83a56e6a914687c302946fec/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_f0ca6b954f28db1d942bcdb5b2347248/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_26036188367f4dd8ab38c6eed0db4614/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_2e5062f7155837f5f4b1990f9bdab5db/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_5826504d8f5d72f5a896ce83b922c98e/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_fe5e3e67b6d7a8cbcdac508a3412d002/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_214f6105212aec6d1895460a9cc6eaed/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_2542aab4e027006d4f4223db5711e791/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._59afb87696a0dce9b123b45a0cbbb3e0/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._f09ca44b075894bef1f22048f2189343/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_57431883aeb2c82f4228eaee26b876b0/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@18.3.18_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_4171a71166cfa012d93ab3a234657034/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_df97a09d62161aad15439954749cb418/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_89c4c6402d5b83b03c66d469fb85e060/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_583becc46742c89b7c85954884e8f11e/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_96e5d54b7bd10cf53cadcc132d77f938/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.6.3_@types+react@18.3.18_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // packages/react/select/src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(BubbleSelect, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    // Prevents the content from going off the starting edge of the\n                    // viewport. It may still go off the ending edge, but this can be\n                    // controlled by the user since they may want to manage overflow in a\n                    // specific way.\n                    // https://github.com/radix-ui/primitives/issues/2049\n                    Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nvar BubbleSelect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", {\n            ...selectProps,\n            ref: composedRefs,\n            defaultValue: value\n        })\n    });\n});\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._c56d354b83a56e6a914687c302946fec/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;