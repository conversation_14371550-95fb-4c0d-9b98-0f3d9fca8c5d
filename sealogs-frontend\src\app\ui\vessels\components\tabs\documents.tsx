'use client'

import FileUpload from '@/components/file-upload'
import FileItem from '@/components/file-item'
import { formatDate } from '@/app/helpers/dateHelper'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { FileText, Upload, Trash2 } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface Document {
    id: string
    title: string
    fileFilename: string
    created: string
}

interface DocumentsTabProps {
    imCrew: boolean
    edit_docs: boolean
    setDocuments: (docs: Document[]) => void
    documents: Document[]
    delete_docs: boolean
    deleteFile: (id: string) => void
}

export default function DocumentsTab({
    imCrew,
    edit_docs,
    setDocuments,
    documents,
    delete_docs,
    deleteFile,
}: DocumentsTabProps) {
    const { toast } = useToast()

    const handleDeleteFile = (documentId: string) => {
        if (!delete_docs) {
            toast({
                title: 'Permission denied',
                description:
                    'You do not have permission to delete this document',
                variant: 'destructive',
            })
            return
        }
        deleteFile(documentId)
    }

    const EmptyState = () => (
        <Card className="border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-muted p-4 mb-4">
                    <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No documents yet</h3>
                <p className="text-sm text-muted-foreground text-center max-w-sm mb-4">
                    {edit_docs && !imCrew
                        ? 'Upload your first document to get started. Drag and drop files or click to browse.'
                        : 'Documents will appear here when they are uploaded.'}
                </p>
                {edit_docs && !imCrew && (
                    <Badge type="normal" variant="secondary" className="gap-1">
                        <Upload className="h-3 w-3" />
                        Ready to upload
                    </Badge>
                )}
            </CardContent>
        </Card>
    )

    return (
        <Card className="space-y-6">
            {/* Upload Section */}
            {!imCrew && edit_docs && (
                <FileUpload
                    setDocuments={(files: any[] | ((prev: any[]) => any[])) => {
                        if (typeof files === 'function') {
                            setDocuments(files(documents))
                        } else {
                            setDocuments(files)
                        }
                    }}
                    text=""
                    subText="Drag files here or upload"
                    documents={documents}
                />
            )}

            {/* Documents List */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <h2 className="text-lg font-semibold">Documents</h2>
                        {documents.length > 0 && (
                            <Badge
                                type="normal"
                                className="text-sm"
                                variant="secondary">
                                {documents.length}{' '}
                                {documents.length === 1 ? 'file' : 'files'}
                            </Badge>
                        )}
                    </div>
                </div>

                {documents.length > 0 ? (
                    <div className="grid gap-3">
                        {documents.map((document) => (
                            <div className="flex group items-center justify-between gap-4">
                                <div className="flex-1 min-w-0">
                                    <FileItem document={document} />
                                </div>

                                <div className="flex items-center gap-3 flex-shrink-0">
                                    <div className="text-right">
                                        <p className="text-xs text-muted-foreground">
                                            {formatDate(document.created)}
                                        </p>
                                    </div>

                                    {delete_docs && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() =>
                                                handleDeleteFile(document.id)
                                            }
                                            className="h-8 w-8 p-0 md:opacity-0 group-hover:md:opacity-100 transition-opacity hover:bg-destructive/10 hover:text-destructive"
                                            aria-label={`Delete ${document.title}`}>
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <EmptyState />
                )}
            </div>
        </Card>
    )
}
