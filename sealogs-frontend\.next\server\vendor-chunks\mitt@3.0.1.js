"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mitt@3.0.1";
exports.ids = ["vendor-chunks/mitt@3.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/mitt@3.0.1/node_modules/mitt/dist/mitt.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/.pnpm/mitt@3.0.1/node_modules/mitt/dist/mitt.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(n){return{all:n=n||new Map,on:function(t,e){var i=n.get(t);i?i.push(e):n.set(t,[e])},off:function(t,e){var i=n.get(t);i&&(e?i.splice(i.indexOf(e)>>>0,1):n.set(t,[]))},emit:function(t,e){var i=n.get(t);i&&i.slice().map(function(n){n(e)}),(i=n.get(\"*\"))&&i.slice().map(function(n){n(t,e)})}}}\n//# sourceMappingURL=mitt.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWl0dEAzLjAuMS9ub2RlX21vZHVsZXMvbWl0dC9kaXN0L21pdHQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBUyxHQUFHLE9BQU8sa0NBQWtDLGVBQWUseUJBQXlCLG1CQUFtQixlQUFlLGdEQUFnRCxvQkFBb0IsZUFBZSw2QkFBNkIsS0FBSyw0Q0FBNEMsT0FBTztBQUN0VCIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vbWl0dEAzLjAuMS9ub2RlX21vZHVsZXMvbWl0dC9kaXN0L21pdHQubWpzPzZkMTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24obil7cmV0dXJue2FsbDpuPW58fG5ldyBNYXAsb246ZnVuY3Rpb24odCxlKXt2YXIgaT1uLmdldCh0KTtpP2kucHVzaChlKTpuLnNldCh0LFtlXSl9LG9mZjpmdW5jdGlvbih0LGUpe3ZhciBpPW4uZ2V0KHQpO2kmJihlP2kuc3BsaWNlKGkuaW5kZXhPZihlKT4+PjAsMSk6bi5zZXQodCxbXSkpfSxlbWl0OmZ1bmN0aW9uKHQsZSl7dmFyIGk9bi5nZXQodCk7aSYmaS5zbGljZSgpLm1hcChmdW5jdGlvbihuKXtuKGUpfSksKGk9bi5nZXQoXCIqXCIpKSYmaS5zbGljZSgpLm1hcChmdW5jdGlvbihuKXtuKHQsZSl9KX19fVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWl0dC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mitt@3.0.1/node_modules/mitt/dist/mitt.mjs\n");

/***/ })

};
;