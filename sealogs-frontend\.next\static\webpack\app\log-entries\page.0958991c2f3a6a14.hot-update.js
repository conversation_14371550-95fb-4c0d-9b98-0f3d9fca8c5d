"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.1_@type_2eea960bf75c3114822a94bf3a751f22/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_triplocation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/triplocation */ \"(app-pages-browser)/./src/app/ui/logbook/components/triplocation.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize currentEvent object to prevent inline object creation\n    const memoizedCurrentEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            geoLocationID: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n            lat: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n            long: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n        }), [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        toast,\n        setLocation,\n        setOpenNewLocationDialog,\n        setCurrentLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0,\n                paxDeparted: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0,\n                vehiclesJoined: isNaN(+(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn)) ? 0 : +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn),\n                vehiclesDeparted: isNaN(+(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff)) ? 0 : +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff),\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 918,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_triplocation__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: memoizedCurrentEvent,\n                    geoLocations: geoLocations\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 927,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 923,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 937,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 959,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 955,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 972,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 991,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 987,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 970,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1011,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1007,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1026,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1005,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1044,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1040,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1065,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1088,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1084,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1108,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1114,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1107,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1129,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1138,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1147,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1146,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1157,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1156,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1122,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 917,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"FcV64c9PeXf8lTDZUaoOswTKPBM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});